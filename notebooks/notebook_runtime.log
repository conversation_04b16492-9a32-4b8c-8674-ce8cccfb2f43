2025-09-15 21:47:29.090 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 19:47:29.090457 to 2025-09-15 19:47:29.090457
2025-09-15 21:47:29.484 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 21:47:44.465 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 21:48:26.357 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 21:48:26.363 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 21:48:26.364 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 21:51:26.275 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 19:51:26.275607 to 2025-09-15 19:51:26.275607
2025-09-15 21:51:26.845 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 21:51:39.386 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 21:51:57.134 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 21:51:57.138 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 21:51:57.139 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 21:51:57.150 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 21:51:57.269 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 22:04:51.741 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 20:04:51.741658 to 2025-09-15 20:04:51.741658
2025-09-15 22:04:51.741 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 20:04:51.741658 to 2025-09-15 20:04:51.741658
2025-09-15 22:04:52.323 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 22:04:52.323 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 22:05:07.535 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 22:05:07.535 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 22:05:07.596 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 22:05:07.596 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 22:05:07.600 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 22:05:07.600 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 22:05:07.601 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 22:05:07.601 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 22:05:07.610 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 22:05:07.610 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 22:05:07.703 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 22:05:07.703 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 22:11:02.485 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 20:11:02.485070 to 2025-09-15 20:11:02.485070
2025-09-15 22:11:02.485 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 20:11:02.485070 to 2025-09-15 20:11:02.485070
2025-09-15 22:11:02.485 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 20:11:02.485070 to 2025-09-15 20:11:02.485070
2025-09-15 22:11:03.059 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 22:11:03.059 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 22:11:03.059 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 22:11:16.646 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 22:11:16.646 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 22:11:16.646 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 22:11:16.704 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 22:11:16.704 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 22:11:16.704 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 22:11:16.709 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 22:11:16.709 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 22:11:16.709 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 22:11:16.711 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 22:11:16.711 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 22:11:16.711 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 22:11:16.721 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 22:11:16.721 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 22:11:16.721 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 22:11:16.816 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 22:11:16.816 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 22:11:16.816 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 23:40:24.940 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 21:40:24.940537 to 2025-09-15 21:40:24.940537
2025-09-15 23:40:26.285 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 23:40:53.205 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 23:40:53.410 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 23:40:53.414 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 23:40:53.415 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 23:40:53.427 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 23:40:53.563 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 23:46:40.123 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 21:46:40.123110 to 2025-09-15 21:46:40.123110
2025-09-15 23:46:40.123 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 21:46:40.123110 to 2025-09-15 21:46:40.123110
2025-09-15 23:46:41.086 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 23:46:41.086 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 23:47:01.684 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 23:47:01.684 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 23:47:01.751 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 23:47:01.751 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 23:47:01.756 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 23:47:01.756 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 23:47:01.757 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 23:47:01.757 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 23:47:01.766 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 23:47:01.766 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 23:47:01.856 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-15 23:47:01.856 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-16 00:14:10.156 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 22:14:10.156174 to 2025-09-15 22:14:10.156174
2025-09-16 00:14:10.156 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 22:14:10.156174 to 2025-09-15 22:14:10.156174
2025-09-16 00:14:10.156 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 22:14:10.156174 to 2025-09-15 22:14:10.156174
2025-09-16 00:14:11.605 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-16 00:14:11.605 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-16 00:14:11.605 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-16 00:14:27.834 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-16 00:14:27.834 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-16 00:14:27.834 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-16 00:14:27.911 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-16 00:14:27.911 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-16 00:14:27.911 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-16 00:14:27.916 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-16 00:14:27.916 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-16 00:14:27.916 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-16 00:14:27.917 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-16 00:14:27.917 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-16 00:14:27.917 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-16 00:14:27.926 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-16 00:14:27.926 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-16 00:14:27.926 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-16 00:14:28.018 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-16 00:14:28.018 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-16 00:14:28.018 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-16 22:15:23.723 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-27 20:15:23.723393 to 2025-09-16 20:15:23.723393
2025-09-16 22:15:24.160 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-27 to 2025-09-16 with interval 1h
2025-09-16 22:15:44.547 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-16 22:15:44.830 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-16 22:15:44.835 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-27 00:00:00  26221.68  26260.00  26202.96  26257.54  737.96661   
1 2023-09-27 01:00:00  26257.54  26265.00  26219.12  26260.27  405.02139   
2 2023-09-27 02:00:00  26260.27  26277.54  26220.40  26228.04  411.59940   
3 2023-09-27 03:00:00  26228.03  26289.50  26227.72  26278.04  349.94497   
4 2023-09-27 04:00:00  26278.03  26289.00  26247.93  26248.71  411.99424   

      close_time quote_asset_volume  number_of_trades  \
0  1695776399999  19358474.59042400             28226   
1  1695779999999  10630621.61621800             19784   
2  1695783599999  10802960.31315990             19036   
3  1695787199999   9189882.53031440             17346   
4  1695790799999  10820319.56514480             18193   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                380.51392000             9981275.63496880      0  
1                187.22352000             4914103.90788480      0  
2                156.67387000             4112459.56050330      0  
3                163.09124000             4282784.56608620      0  
4                132.89569000             3490503.78926150      0  
2025-09-16 22:15:44.836 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-16 22:15:44.847 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-16 22:15:44.961 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-16 22:17:46.368 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-27 20:17:46.368596 to 2025-09-16 20:17:46.368596
2025-09-16 22:17:46.826 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-27 to 2025-09-16 with interval 1h
2025-09-16 22:18:00.498 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-16 22:18:00.657 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-16 22:18:00.661 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-27 00:00:00  26221.68  26260.00  26202.96  26257.54  737.96661   
1 2023-09-27 01:00:00  26257.54  26265.00  26219.12  26260.27  405.02139   
2 2023-09-27 02:00:00  26260.27  26277.54  26220.40  26228.04  411.59940   
3 2023-09-27 03:00:00  26228.03  26289.50  26227.72  26278.04  349.94497   
4 2023-09-27 04:00:00  26278.03  26289.00  26247.93  26248.71  411.99424   

      close_time quote_asset_volume  number_of_trades  \
0  1695776399999  19358474.59042400             28226   
1  1695779999999  10630621.61621800             19784   
2  1695783599999  10802960.31315990             19036   
3  1695787199999   9189882.53031440             17346   
4  1695790799999  10820319.56514480             18193   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                380.51392000             9981275.63496880      0  
1                187.22352000             4914103.90788480      0  
2                156.67387000             4112459.56050330      0  
3                163.09124000             4282784.56608620      0  
4                132.89569000             3490503.78926150      0  
2025-09-16 22:18:00.662 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-16 22:18:00.671 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-16 22:18:00.760 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
2025-09-16 22:49:10.908 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-27 20:49:10.907940 to 2025-09-16 20:49:10.907940
2025-09-16 22:49:11.346 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-27 to 2025-09-16 with interval 1h
2025-09-16 22:49:24.036 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-17 12:21:46.009 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-28 10:21:46.008953 to 2025-09-17 10:21:46.008953
2025-09-17 12:21:46.418 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-28 to 2025-09-17 with interval 1h
2025-09-17 12:21:58.980 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
