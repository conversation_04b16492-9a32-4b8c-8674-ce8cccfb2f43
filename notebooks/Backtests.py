import numpy as np
import pandas as pd
from typing import List, Tuple, Literal

class TradingSimulator:
    def __init__(self, initial_capital: float = 100):
        self.initial_capital = initial_capital
        self.transaction_cost_pct = 0.001
        self.stop_loss_pct = 0.02
        self.take_profit_pct = 0.03
        self.hours_per_year = 24 * 365  # Market is open 24/7

    def apply_slippage(self, price: float, atr: float) -> float:
        slippage = np.random.normal(0, 0.1 * atr)
        return max(0, price + slippage)

    def calculate_position_size(self, confidence: float, capital: float, price: float, atr: float) -> float:
        # Scale risk per trade based on confidence
        base_risk = 0.01  # 1% base risk
        max_risk = 0.02   # 2% maximum risk
        risk_per_trade = base_risk + (max_risk - base_risk) * confidence
        risk_amount = risk_per_trade * capital

        # Calculate stop loss based on ATR
        stop_loss = max(self.stop_loss_pct * price, 2 * atr)  # Use larger of percentage or 2*ATR

        # Calculate base position size
        position_size = risk_amount / stop_loss

        # Adjust maximum position size based on confidence
        max_position_pct = 0.3 + 0.2 * confidence  # Scales between 30% to 50% of capital
        max_position = max_position_pct * capital / price

        # Return the smaller of calculated position size and maximum allowed
        return min(position_size, max_position)

    def simulate_trading(self, history: pd.DataFrame, model, scaler, features: List[str]) -> Tuple[float, List[float], pd.DataFrame]:
        capital = self.initial_capital
        position = 0
        entry_price = 0
        position_type: Literal['long', 'short', 'none'] = 'none'
        trades = []
        trade_times = []

        
        
        for i in range(len(history)):
            current_price = history.iloc[i]['close']
            atr = history.iloc[i]['atr']
            
            X = scaler.transform(history.iloc[[i]][features])
            prediction = model.predict(X)[0]
            confidence = model.predict_proba(X)[0][1]
            
            position_size = self.calculate_position_size(confidence, capital, current_price, atr)
            
            # Check for stop loss or take profit
            if position_type != 'none':
                if position_type == 'long':
                    stop_loss_price = entry_price * (1 - self.stop_loss_pct)
                    take_profit_price = entry_price * (1 + self.take_profit_pct)
                    if current_price <= stop_loss_price or current_price >= take_profit_price:
                        exit_price = self.apply_slippage(current_price, atr)
                        revenue = position * exit_price * (1 - self.transaction_cost_pct)
                        trade_return = (revenue - (position * entry_price)) / (position * entry_price)
                        trades.append(trade_return)
                        trade_times.append(history.index[i])
                        capital += revenue
                        position = 0
                        position_type = 'none'
                elif position_type == 'short':
                    stop_loss_price = entry_price * (1 + self.stop_loss_pct)
                    take_profit_price = entry_price * (1 - self.take_profit_pct)
                    if current_price >= stop_loss_price or current_price <= take_profit_price:
                        exit_price = self.apply_slippage(current_price, atr)
                        revenue = position * (2 * entry_price - exit_price) * (1 - self.transaction_cost_pct)
                        trade_return = (revenue - (position * entry_price)) / (position * entry_price)
                        trades.append(trade_return)
                        trade_times.append(history.index[i])
                        capital += revenue
                        position = 0
                        position_type = 'none'
            
            if position_type == 'none':
                if prediction == 1:  # Buy signal
                    entry_price = self.apply_slippage(current_price, atr)
                    cost = position_size * entry_price * (1 + self.transaction_cost_pct)
                    if capital >= cost:
                        position = position_size
                        capital -= cost
                        position_type = 'long'
                elif prediction == 0:  # Sell signal (Short)
                    entry_price = self.apply_slippage(current_price, atr)
                    cost = position_size * entry_price * (1 + self.transaction_cost_pct)
                    if capital >= cost:
                        position = position_size
                        capital -= cost
                        position_type = 'short'
            
            # Ensure no negative capital
            capital = max(capital, 0)
            
            # Log the capital and position after each trade
            history.loc[history.index[i], 'current_capital'] = capital
            history.loc[history.index[i], 'current_position'] = position
            history.loc[history.index[i], 'position_type'] = position_type
        
        return capital, trades, trade_times, history

    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        excess_returns = returns - risk_free_rate / self.hours_per_year
        if excess_returns.std() != 0:
            sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(self.hours_per_year)
        else:
            sharpe_ratio = 0  # or np.inf, depending on your preference
        return sharpe_ratio

    def calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        excess_returns = returns - risk_free_rate / self.hours_per_year
        downside_returns = excess_returns[excess_returns < 0]
        if downside_returns.std() != 0:
            sortino_ratio = excess_returns.mean() / downside_returns.std() * np.sqrt(self.hours_per_year)
        else:
            sortino_ratio = 0  # or np.inf, depending on your preference
        return sortino_ratio

    def calculate_metrics(self, final_capital: float, trades: List[float], trade_times: List, history: pd.DataFrame) -> dict:
        total_return = (final_capital - self.initial_capital) / self.initial_capital
        win_rate = sum(1 for trade in trades if trade > 0) / len(trades) if trades else 0
        average_win = sum(trade for trade in trades if trade > 0) / sum(1 for trade in trades if trade > 0) if trades else 0
        average_loss = sum(trade for trade in trades if trade < 0) / sum(1 for trade in trades if trade < 0) if trades else 0
        profit_factor = -sum(trade for trade in trades if trade > 0) / sum(trade for trade in trades if trade < 0) if trades else 0
        
        # Create a Series of trade returns with timestamps
        # Convert trade_times to DatetimeIndex if necessary
        trade_times = pd.to_datetime(trade_times)
        trade_returns = pd.Series(trades, index=trade_times)

        # Ensure trade_times is in DatetimeIndex format
        if not isinstance(trade_returns.index, pd.DatetimeIndex):
            raise ValueError("trade_times must be a DatetimeIndex.")

        # Resample trade returns to hourly frequency, filling non-trade hours with 0
        hourly_returns = trade_returns.resample('H').sum().fillna(0)
        
        # Calculate Sharpe and Sortino ratios
        sharpe_ratio = self.calculate_sharpe_ratio(hourly_returns)
        sortino_ratio = self.calculate_sortino_ratio(hourly_returns)
        
        # Calculate max drawdown
        cumulative_returns = (1 + hourly_returns).cumprod()
        peak = cumulative_returns.expanding(min_periods=1).max()
        drawdown = (cumulative_returns / peak) - 1
        max_drawdown = drawdown.min()

        
        # Ensure the timestamp column is converted to datetime and set as the index
        history['date'] = pd.to_datetime(history['date'])
        history.set_index('date', inplace=True)

        # Now when calculating total_hours, this will work correctly
        total_hours = (history.index[-1] - history.index[0]).total_seconds() / 3600
        annualized_return = (1 + total_return) ** (self.hours_per_year / total_hours) - 1

        # Print intermediate values for debugging
        print(f"Mean hourly return: {hourly_returns.mean()}")
        print(f"Std dev of hourly returns: {hourly_returns.std()}")
        print(f"Number of trades: {len(trades)}")
        print(f"Annualization factor: {np.sqrt(self.hours_per_year)}")
        print(f"Min return: {hourly_returns.min()}")
        print(f"Max return: {hourly_returns.max()}")
        print(f"Return distribution: \n{hourly_returns.describe()}")

        # Calculate rolling Sharpe ratio
        rolling_sharpe = hourly_returns.rolling(window=30*24).apply(lambda x: self.calculate_sharpe_ratio(x))
        print(f"Rolling Sharpe ratio: \n{rolling_sharpe.describe()}")

        return {
            "Final Capital": final_capital,
            "Total Return": total_return,
            "Annualized Return": annualized_return,
            "Win Rate": win_rate,
            "Average Win": average_win,
            "Average Loss": average_loss,
            "Profit Factor": profit_factor,
            "Sharpe Ratio": sharpe_ratio,
            "Sortino Ratio": sortino_ratio,
            "Max Drawdown": max_drawdown
        }

    def run_simulation(self, history: pd.DataFrame, model, scaler, features: List[str]) -> Tuple[dict, pd.DataFrame]:
        final_capital, trades, trade_times, updated_history = self.simulate_trading(history, model, scaler, features)
        metrics = self.calculate_metrics(final_capital, trades, trade_times, updated_history)
        return metrics, updated_history

# Usage example:
# simulator = TradingSimulator()
# metrics, updated_history = simulator.run_simulation(history, best_model, scaler, features)
# print("\nTrading Simulation Results:")
# for key, value in metrics.items():
#     print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")