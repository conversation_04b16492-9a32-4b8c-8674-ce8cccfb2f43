import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from loguru import logger

# Add project root to sys.path
# This is a common pattern to make project modules importable in notebooks
module_path = os.path.abspath(os.path.join('..'))
if module_path not in sys.path:
    sys.path.append(module_path)

# Import necessary components from our application
from app.services.backtesting.data_fetcher import BinanceDataFetcher
from app.services.models.xgboost_strategy import xgboost_strategy

# Scikit-learn and XGBoost
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

# Configure logger
logger.add("notebook_runtime.log", rotation="500 MB")

print("Setup complete. Modules imported and path configured.")

# Initialize the data fetcher
fetcher = BinanceDataFetcher()

# Define parameters
symbol = 'BTC/USDT'
timeframe = '1h'
days = 720
end_date = datetime.utcnow()
start_date = end_date - timedelta(days=days)

# Fetch the data
logger.info(f"Fetching data for {symbol} from {start_date} to {end_date}")
history_df = fetcher.fetch(symbol, timeframe, start_date, end_date)

# Display the first few rows and shape of the dataframe
print("Data fetched successfully.")
print("Shape of the dataframe:", history_df.shape)
history_df.head()

# Initialize the strategy to access the feature generation method
# Note: This will load the v1 model, but we are only using it for its `generate_features` method.
strategy = xgboost_strategy()

# Generate features
featured_df = strategy.generate_features(history_df.copy())

# Display the dataframe with new features
print("Features generated.")
print("Shape of the featured dataframe:", featured_df.shape)
featured_df.head()

# Define the target variable 'y'
# We predict if the next period's close is higher than the current period's close
featured_df['target'] = (featured_df['close'].shift(-1) > featured_df['close']).astype(int)

# Drop the last row since it will have a NaN target
featured_df.dropna(subset=['target'], inplace=True)

# Define features (X) and target (y)
features = strategy.features
X = featured_df[features]
y = featured_df['target']

print("Target variable 'y' created.")
print("Shape of X:", X.shape)
print("Shape of y:", y.shape)
print("\nValue counts of target variable:")
print(y.value_counts())

# Use TimeSeriesSplit for cross-validation
tscv = TimeSeriesSplit(n_splits=5)

# Get the last split for final train/test
for train_index, test_index in tscv.split(X):
    X_train, X_test = X.iloc[train_index], X.iloc[test_index]
    y_train, y_test = y.iloc[train_index], y.iloc[test_index]

print("Data split into training and testing sets.")
print("X_train shape:", X_train.shape)
print("X_test shape:", X_test.shape)
print("y_train shape:", y_train.shape)
print("y_test shape:", y_test.shape)

# 1. Initialize the StandardScaler
scaler = StandardScaler()

# 2. Fit the scaler on the training data and transform it
X_train_scaled = scaler.fit_transform(X_train)

# 3. Transform the test data using the same scaler
X_test_scaled = scaler.transform(X_test)

print("Features scaled correctly.")

# Initialize and train the XGBClassifier
model = XGBClassifier(
    n_estimators=100,
    max_depth=3,
    learning_rate=0.1,
    use_label_encoder=False,
    eval_metric='logloss'
)

print("Training XGBoost model...")
model.fit(X_train_scaled, y_train)
print("Model training complete.")

# Make predictions on the scaled test set
y_pred = model.predict(X_test_scaled)

# Print classification report
print("Classification Report:")
print(classification_report(y_test, y_pred))

# Print confusion matrix
print("\nConfusion Matrix:")
cm = confusion_matrix(y_test, y_pred)
print(cm)

# Visualize confusion matrix
fig = go.Figure(data=go.Heatmap(
                   z=cm,
                   x=['Predicted 0', 'Predicted 1'],
                   y=['Actual 0', 'Actual 1'],
                   hoverongaps=False))
fig.update_layout(title='Confusion Matrix')
fig.show()

# Define output paths
model_dir = os.path.join(module_path, 'app', 'services', 'models')
model_path = os.path.join(model_dir, 'XGBClassifier_v2.pkl')
scaler_path = os.path.join(model_dir, 'StandardScaler_v2.pkl')

# Save the model
joblib.dump(model, model_path)
print(f"Model saved to: {model_path}")

# Save the scaler
joblib.dump(scaler, scaler_path)
print(f"Scaler saved to: {scaler_path}")

# Additional imports for advanced features
import talib
from scipy import stats
from scipy.signal import hilbert
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.model_selection import cross_val_score
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.decomposition import PCA
from sklearn.preprocessing import RobustScaler, QuantileTransformer
import warnings
warnings.filterwarnings('ignore')

print("Advanced libraries imported successfully.")

!pip install TA-Lib


def create_advanced_features(df):
    """
    Create state-of-the-art features for BTC prediction.
    """
    df_advanced = df.copy()
    
    # Basic OHLCV
    high = df_advanced['high'].values
    low = df_advanced['low'].values
    close = df_advanced['close'].values
    volume = df_advanced['volume'].values
    open_price = df_advanced['open'].values
    
    # 1. Market Microstructure Features
    df_advanced['vwap'] = talib.AVGPRICE(open_price, high, low, close)
    df_advanced['volume_sma_ratio'] = volume / talib.SMA(volume, timeperiod=20)
    df_advanced['price_volume_trend'] = talib.AD(high, low, close, volume)
    df_advanced['money_flow_index'] = talib.MFI(high, low, close, volume, timeperiod=14)
    df_advanced['chaikin_oscillator'] = talib.ADOSC(high, low, close, volume, fastperiod=3, slowperiod=10)
    
    # 2. Advanced Momentum Indicators
    df_advanced['rsi_14'] = talib.RSI(close, timeperiod=14)
    df_advanced['rsi_21'] = talib.RSI(close, timeperiod=21)
    df_advanced['stoch_k'], df_advanced['stoch_d'] = talib.STOCH(high, low, close)
    df_advanced['williams_r'] = talib.WILLR(high, low, close, timeperiod=14)
    df_advanced['ultimate_oscillator'] = talib.ULTOSC(high, low, close)
    df_advanced['commodity_channel_index'] = talib.CCI(high, low, close, timeperiod=14)
    
    # 3. Volatility and Regime Features
    df_advanced['atr_14'] = talib.ATR(high, low, close, timeperiod=14)
    df_advanced['atr_21'] = talib.ATR(high, low, close, timeperiod=21)
    df_advanced['volatility_ratio'] = df_advanced['atr_14'] / df_advanced['atr_21']
    
    # Realized volatility (rolling standard deviation of returns)
    returns = np.log(close / np.roll(close, 1))
    df_advanced['realized_vol_10'] = pd.Series(returns).rolling(10).std()
    df_advanced['realized_vol_30'] = pd.Series(returns).rolling(30).std()
    df_advanced['vol_regime'] = (df_advanced['realized_vol_10'] > df_advanced['realized_vol_30']).astype(int)
    
    # 4. Trend and Pattern Recognition
    df_advanced['adx'] = talib.ADX(high, low, close, timeperiod=14)
    df_advanced['plus_di'] = talib.PLUS_DI(high, low, close, timeperiod=14)
    df_advanced['minus_di'] = talib.MINUS_DI(high, low, close, timeperiod=14)
    df_advanced['aroon_up'], df_advanced['aroon_down'] = talib.AROON(high, low, timeperiod=14)
    df_advanced['aroon_oscillator'] = df_advanced['aroon_up'] - df_advanced['aroon_down']
    
    # 5. Multi-timeframe Features (simulated by different periods)
    for period in [5, 10, 20, 50]:
        df_advanced[f'sma_{period}'] = talib.SMA(close, timeperiod=period)
        df_advanced[f'ema_{period}'] = talib.EMA(close, timeperiod=period)
        df_advanced[f'price_sma_ratio_{period}'] = close / df_advanced[f'sma_{period}']
        df_advanced[f'ema_slope_{period}'] = df_advanced[f'ema_{period}'].diff(5)
    
    # 6. Fractal and Complexity Features
    def hurst_exponent(ts, max_lag=20):
        """Calculate Hurst exponent"""
        lags = range(2, max_lag)
        tau = [np.sqrt(np.std(np.subtract(ts[lag:], ts[:-lag]))) for lag in lags]
        poly = np.polyfit(np.log(lags), np.log(tau), 1)
        return poly[0] * 2.0
    
    # Rolling Hurst exponent
    hurst_window = 50
    hurst_values = []
    for i in range(len(close)):
        if i < hurst_window:
            hurst_values.append(np.nan)
        else:
            try:
                h = hurst_exponent(close[i-hurst_window:i])
                hurst_values.append(h)
            except:
                hurst_values.append(np.nan)
    
    df_advanced['hurst_exponent'] = hurst_values
    
    # 7. Market Structure Features
    df_advanced['higher_highs'] = (high > np.roll(high, 1)).astype(int)
    df_advanced['lower_lows'] = (low < np.roll(low, 1)).astype(int)
    df_advanced['inside_bar'] = ((high <= np.roll(high, 1)) & (low >= np.roll(low, 1))).astype(int)
    df_advanced['outside_bar'] = ((high >= np.roll(high, 1)) & (low <= np.roll(low, 1))).astype(int)
    
    # 8. Statistical Features
    for window in [10, 20, 30]:
        df_advanced[f'skewness_{window}'] = pd.Series(returns).rolling(window).skew()
        df_advanced[f'kurtosis_{window}'] = pd.Series(returns).rolling(window).kurt()
        df_advanced[f'zscore_{window}'] = (close - pd.Series(close).rolling(window).mean()) / pd.Series(close).rolling(window).std()
    
    # 9. Cyclical Features (time-based)
    if 'timestamp' in df_advanced.columns:
        df_advanced['hour'] = pd.to_datetime(df_advanced['timestamp']).dt.hour
        df_advanced['day_of_week'] = pd.to_datetime(df_advanced['timestamp']).dt.dayofweek
        df_advanced['hour_sin'] = np.sin(2 * np.pi * df_advanced['hour'] / 24)
        df_advanced['hour_cos'] = np.cos(2 * np.pi * df_advanced['hour'] / 24)
        df_advanced['dow_sin'] = np.sin(2 * np.pi * df_advanced['day_of_week'] / 7)
        df_advanced['dow_cos'] = np.cos(2 * np.pi * df_advanced['day_of_week'] / 7)
    
    # 10. Interaction Features
    df_advanced['rsi_volume_interaction'] = df_advanced['rsi_14'] * df_advanced['volume_sma_ratio']
    df_advanced['volatility_momentum'] = df_advanced['atr_14'] * df_advanced['adx']
    df_advanced['trend_strength'] = df_advanced['adx'] * abs(df_advanced['aroon_oscillator'])
    
    return df_advanced

# Apply advanced feature engineering
print("Creating advanced features...")
advanced_df = create_advanced_features(featured_df.copy())

# Remove infinite and NaN values
advanced_df = advanced_df.replace([np.inf, -np.inf], np.nan)
advanced_df = advanced_df.dropna()

print(f"Advanced features created. Shape: {advanced_df.shape}")
print(f"Number of features: {len([col for col in advanced_df.columns if col not in ['timestamp', 'target', 'open', 'high', 'low', 'close', 'volume']])}")

# Prepare feature matrix
# Exclude non-numeric and problematic columns
exclude_columns = ['timestamp', 'target', 'open', 'high', 'low', 'close', 'volume', 
                   'close_time', 'date', 'quote_asset_volume', 'taker_buy_base_asset_volume', 
                   'taker_buy_quote_asset_volume', 'ignore', 'number_of_trades']

feature_columns = [col for col in advanced_df.columns if col not in exclude_columns]

# Ensure all selected columns are numeric
X_advanced = advanced_df[feature_columns].copy()

# Convert any remaining object columns to numeric, coercing errors to NaN
for col in X_advanced.columns:
    if X_advanced[col].dtype == 'object':
        X_advanced[col] = pd.to_numeric(X_advanced[col], errors='coerce')

# Drop any columns that are all NaN after conversion
X_advanced = X_advanced.dropna(axis=1, how='all')

# Fill any remaining NaN values with 0
X_advanced = X_advanced.fillna(0)

y_advanced = advanced_df['target']

print(f"Total features before selection: {len(X_advanced.columns)}")
print(f"Data types check:")
print(X_advanced.dtypes.value_counts())
print()
# Split data for feature selection
tscv_advanced = TimeSeriesSplit(n_splits=5)
for train_idx, test_idx in tscv_advanced.split(X_advanced):
    X_train_adv, X_test_adv = X_advanced.iloc[train_idx], X_advanced.iloc[test_idx]
    y_train_adv, y_test_adv = y_advanced.iloc[train_idx], y_advanced.iloc[test_idx]

print("Data split into training and testing sets for advanced features.")
print("X_train_adv shape:", X_train_adv.shape)
print("X_train_adv dtypes:", X_train_adv.dtypes.value_counts())

# 1. Statistical Feature Selection (F-test)
selector_f = SelectKBest(score_func=f_classif, k=50)
X_train_f = selector_f.fit_transform(X_train_adv, y_train_adv)
selected_features_f = X_train_adv.columns[selector_f.get_support()].tolist()

# 2. Recursive Feature Elimination with Random Forest
rf_selector = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)

rfe = RFE(estimator=rf_selector, n_features_to_select=50, step=1)
rfe.fit(X_train_adv, y_train_adv)
selected_features_rfe = X_train_adv.columns[rfe.support_].tolist()

# 3. Feature Importance from XGBoost
xgb_selector = XGBClassifier(n_estimators=100, random_state=42, eval_metric='logloss')
xgb_selector.fit(X_train_adv, y_train_adv)
feature_importance = pd.DataFrame({
    'feature': X_train_adv.columns,
    'importance': xgb_selector.feature_importances_
}).sort_values('importance', ascending=False)

selected_features_xgb = feature_importance.head(50)['feature'].tolist()

# Combine feature selections (union of top features)
selected_features_combined = list(set(selected_features_f + selected_features_rfe + selected_features_xgb))

print(f"Features selected by F-test: {len(selected_features_f)}")
print(f"Features selected by RFE: {len(selected_features_rfe)}")
print(f"Features selected by XGBoost importance: {len(selected_features_xgb)}")
print(f"Combined unique features: {len(selected_features_combined)}")

# Use combined features
X_selected = X_advanced[selected_features_combined]
X_train_selected = X_train_adv[selected_features_combined]
X_test_selected = X_test_adv[selected_features_combined]

print("\nTop 10 most important features:")
print(feature_importance.head(10))

# 1. Robust Scaling (less sensitive to outliers)
robust_scaler = RobustScaler()
X_train_robust = robust_scaler.fit_transform(X_train_selected)
X_test_robust = robust_scaler.transform(X_test_selected)

# 2. Quantile Transformation (makes features more Gaussian)
quantile_transformer = QuantileTransformer(output_distribution='normal', random_state=42)
X_train_quantile = quantile_transformer.fit_transform(X_train_selected)
X_test_quantile = quantile_transformer.transform(X_test_selected)

# 3. PCA for dimensionality reduction (optional - keeping 95% variance)
pca = PCA(n_components=0.95, random_state=42)
X_train_pca = pca.fit_transform(X_train_robust)
X_test_pca = pca.transform(X_test_robust)

print(f"Original features: {X_train_selected.shape[1]}")
print(f"PCA components (95% variance): {X_train_pca.shape[1]}")
print(f"Explained variance ratio: {pca.explained_variance_ratio_.sum():.4f}")

# We'll use robust scaling for our main models
X_train_final = X_train_robust
X_test_final = X_test_robust

print("Advanced preprocessing completed.")

# Define base models with optimized hyperparameters
base_models = {
    'xgboost': XGBClassifier(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        eval_metric='logloss'
    ),
    'random_forest': RandomForestClassifier(
        n_estimators=200,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    ),
    'gradient_boosting': GradientBoostingClassifier(
        n_estimators=200,
        max_depth=5,
        learning_rate=0.05,
        subsample=0.8,
        random_state=42
    ),
    'neural_network': MLPClassifier(
        hidden_layer_sizes=(100, 50, 25),
        activation='relu',
        solver='adam',
        alpha=0.001,
        learning_rate='adaptive',
        max_iter=500,
        random_state=42
    ),
    'logistic_regression': LogisticRegression(
        C=1.0,
        penalty='l2',
        solver='liblinear',
        random_state=42
    )
}

# Train and evaluate individual models
individual_scores = {}
trained_models = {}

print("Training individual models...")
for name, model in base_models.items():
    print(f"Training {name}...")
    
    # Train model
    model.fit(X_train_final, y_train_adv)
    trained_models[name] = model
    
    # Evaluate
    y_pred = model.predict(X_test_final)
    accuracy = accuracy_score(y_test_adv, y_pred)
    precision = precision_score(y_test_adv, y_pred)
    recall = recall_score(y_test_adv, y_pred)
    f1 = f1_score(y_test_adv, y_pred)
    
    individual_scores[name] = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }
    
    print(f"{name} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")

print("\nIndividual model training completed.")

# Create ensemble models
print("Creating ensemble models...")

# 1. Voting Classifier (Hard Voting)
voting_hard = VotingClassifier(
    estimators=[(name, model) for name, model in base_models.items()],
    voting='hard'
)

# 2. Voting Classifier (Soft Voting) - uses predicted probabilities
voting_soft = VotingClassifier(
    estimators=[(name, model) for name, model in base_models.items()],
    voting='soft'
)

# 3. Stacking Classifier - uses a meta-learner
stacking_classifier = StackingClassifier(
    estimators=[(name, model) for name, model in base_models.items()],
    final_estimator=LogisticRegression(random_state=42),
    cv=3  # Use 3-fold CV for stacking
)

# Train ensemble models
ensemble_models = {
    'voting_hard': voting_hard,
    'voting_soft': voting_soft,
    'stacking': stacking_classifier
}

ensemble_scores = {}

for name, ensemble in ensemble_models.items():
    print(f"Training {name} ensemble...")
    
    # Train ensemble
    ensemble.fit(X_train_final, y_train_adv)
    
    # Evaluate
    y_pred_ensemble = ensemble.predict(X_test_final)
    accuracy = accuracy_score(y_test_adv, y_pred_ensemble)
    precision = precision_score(y_test_adv, y_pred_ensemble)
    recall = recall_score(y_test_adv, y_pred_ensemble)
    f1 = f1_score(y_test_adv, y_pred_ensemble)
    
    ensemble_scores[name] = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }
    
    print(f"{name} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")

print("\nEnsemble model training completed.")

# Comprehensive model comparison
all_scores = {**individual_scores, **ensemble_scores}

# Create comparison DataFrame
comparison_df = pd.DataFrame(all_scores).T
comparison_df = comparison_df.sort_values('f1', ascending=False)

print("=== MODEL PERFORMANCE COMPARISON ===")
print(comparison_df.round(4))

# Find best model
best_model_name = comparison_df.index[0]
best_f1_score = comparison_df.loc[best_model_name, 'f1']

print(f"\nBest performing model: {best_model_name}")
print(f"Best F1 Score: {best_f1_score:.4f}")

# Get the best model
if best_model_name in trained_models:
    best_model = trained_models[best_model_name]
else:
    best_model = ensemble_models[best_model_name]

# Cross-validation for best model
print(f"\nPerforming cross-validation for {best_model_name}...")
cv_scores = cross_val_score(best_model, X_train_final, y_train_adv, cv=5, scoring='f1')
print(f"CV F1 Scores: {cv_scores}")
print(f"Mean CV F1: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

# Feature importance for interpretability (if available)
if hasattr(best_model, 'feature_importances_'):
    feature_importance_best = pd.DataFrame({
        'feature': selected_features_combined,
        'importance': best_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\nTop 15 most important features for {best_model_name}:")
    print(feature_importance_best.head(15))
    
    # Plot feature importance
    fig = px.bar(
        feature_importance_best.head(20),
        x='importance',
        y='feature',
        orientation='h',
        title=f'Top 20 Feature Importances - {best_model_name}'
    )
    fig.update_layout(height=600)
    fig.show()

# Detailed confusion matrix for best model
y_pred_best = best_model.predict(X_test_final)
cm_best = confusion_matrix(y_test_adv, y_pred_best)

print(f"\nDetailed Classification Report for {best_model_name}:")
print(classification_report(y_test_adv, y_pred_best))

# Enhanced confusion matrix visualization
fig = go.Figure(data=go.Heatmap(
    z=cm_best,
    x=['Predicted Sell/Hold', 'Predicted Buy'],
    y=['Actual Sell/Hold', 'Actual Buy'],
    hoverongaps=False,
    colorscale='Blues',
    text=cm_best,
    texttemplate="%{text}",
    textfont={"size":16}
))

fig.update_layout(
    title=f'Confusion Matrix - {best_model_name}',
    xaxis_title='Predicted',
    yaxis_title='Actual'
)
fig.show()

# Get prediction probabilities for better decision making
if hasattr(best_model, 'predict_proba'):
    y_proba_best = best_model.predict_proba(X_test_final)[:, 1]  # Probability of class 1 (buy)
else:
    y_proba_best = best_model.decision_function(X_test_final)

# Create trading simulation
test_data = advanced_df.iloc[test_idx].copy()
test_data['prediction'] = y_pred_best
test_data['prediction_proba'] = y_proba_best

# Calculate returns for different confidence thresholds
confidence_thresholds = [0.5, 0.6, 0.7, 0.8]
trading_results = {}

for threshold in confidence_thresholds:
    # Only trade when confidence is above threshold
    high_confidence_mask = test_data['prediction_proba'] >= threshold
    
    if high_confidence_mask.sum() == 0:
        continue
    
    # Calculate returns
    test_data['returns'] = test_data['close'].pct_change()
    test_data['strategy_returns'] = 0
    
    # Apply strategy: buy when prediction is 1 and confidence > threshold
    buy_signals = (test_data['prediction'] == 1) & high_confidence_mask
    test_data.loc[buy_signals, 'strategy_returns'] = test_data.loc[buy_signals, 'returns']
    
    # Calculate cumulative returns
    cumulative_returns = (1 + test_data['strategy_returns']).cumprod()
    total_return = cumulative_returns.iloc[-1] - 1
    
    # Calculate Sharpe ratio (assuming 252 trading periods per year)
    sharpe_ratio = test_data['strategy_returns'].mean() / test_data['strategy_returns'].std() * np.sqrt(252)
    
    # Calculate maximum drawdown
    rolling_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # Number of trades
    num_trades = buy_signals.sum()
    
    # Win rate
    winning_trades = (test_data.loc[buy_signals, 'returns'] > 0).sum()
    win_rate = winning_trades / num_trades if num_trades > 0 else 0
    
    trading_results[threshold] = {
        'total_return': total_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'num_trades': num_trades,
        'win_rate': win_rate
    }

# Display trading results
print("=== TRADING PERFORMANCE BY CONFIDENCE THRESHOLD ===")
trading_df = pd.DataFrame(trading_results).T
print(trading_df.round(4))

# Plot cumulative returns for best threshold
if trading_results:
    best_threshold = max(trading_results.keys(), key=lambda x: trading_results[x]['sharpe_ratio'])
    print(f"\nBest confidence threshold: {best_threshold}")
    
    # Recalculate for visualization
    high_confidence_mask = test_data['prediction_proba'] >= best_threshold
    buy_signals = (test_data['prediction'] == 1) & high_confidence_mask
    test_data['strategy_returns'] = 0
    test_data.loc[buy_signals, 'strategy_returns'] = test_data.loc[buy_signals, 'returns']
    
    # Calculate cumulative returns
    test_data['cumulative_strategy'] = (1 + test_data['strategy_returns']).cumprod()
    test_data['cumulative_market'] = (1 + test_data['returns']).cumprod()
    
    # Plot comparison
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=test_data.index,
        y=test_data['cumulative_strategy'],
        mode='lines',
        name=f'Strategy (threshold={best_threshold})',
        line=dict(color='green', width=2)
    ))
    
    fig.add_trace(go.Scatter(
        x=test_data.index,
        y=test_data['cumulative_market'],
        mode='lines',
        name='Buy & Hold',
        line=dict(color='blue', width=2)
    ))
    
    fig.update_layout(
        title='Strategy vs Buy & Hold Performance',
        xaxis_title='Time',
        yaxis_title='Cumulative Returns',
        hovermode='x unified'
    )
    
    fig.show()

# Create model artifacts directory
advanced_model_dir = os.path.join(module_path, 'app', 'services', 'models', 'advanced')
os.makedirs(advanced_model_dir, exist_ok=True)

# Save the best model
best_model_path = os.path.join(advanced_model_dir, f'{best_model_name}_v3.pkl')
joblib.dump(best_model, best_model_path)
print(f"Best model ({best_model_name}) saved to: {best_model_path}")

# Save the robust scaler
scaler_path = os.path.join(advanced_model_dir, 'RobustScaler_v3.pkl')
joblib.dump(robust_scaler, scaler_path)
print(f"Robust scaler saved to: {scaler_path}")

# Save feature selection information
feature_info = {
    'selected_features': selected_features_combined,
    'feature_importance': feature_importance_best.to_dict() if 'feature_importance_best' in locals() else None,
    'num_features': len(selected_features_combined),
    'selection_methods': ['f_test', 'rfe', 'xgboost_importance']
}

feature_info_path = os.path.join(advanced_model_dir, 'feature_info_v3.pkl')
joblib.dump(feature_info, feature_info_path)
print(f"Feature information saved to: {feature_info_path}")

# Save model performance metrics
model_metrics = {
    'best_model_name': best_model_name,
    'best_f1_score': best_f1_score,
    'all_model_scores': comparison_df.to_dict(),
    'cv_scores': cv_scores.tolist(),
    'trading_results': trading_results,
    'best_confidence_threshold': best_threshold if 'best_threshold' in locals() else 0.5
}

metrics_path = os.path.join(advanced_model_dir, 'model_metrics_v3.pkl')
joblib.dump(model_metrics, metrics_path)
print(f"Model metrics saved to: {metrics_path}")

# Save preprocessing components
preprocessing_components = {
    'robust_scaler': robust_scaler,
    'quantile_transformer': quantile_transformer,
    'pca': pca,
    'feature_selector_f': selector_f,
    'feature_selector_rfe': rfe
}

preprocessing_path = os.path.join(advanced_model_dir, 'preprocessing_components_v3.pkl')
joblib.dump(preprocessing_components, preprocessing_path)
print(f"Preprocessing components saved to: {preprocessing_path}")

# Create a summary report
summary_report = f"""
=== STATE-OF-THE-ART BTC PREDICTION MODEL SUMMARY ===

Best Model: {best_model_name}
F1 Score: {best_f1_score:.4f}
Cross-Validation F1: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})

Features Used: {len(selected_features_combined)}
Training Samples: {X_train_final.shape[0]}
Test Samples: {X_test_final.shape[0]}

Model Components:
- Advanced feature engineering with {len(feature_columns)} initial features
- Multi-method feature selection
- Robust scaling for outlier resistance
- Ensemble modeling with {len(base_models)} base models

Trading Performance:
Best Confidence Threshold: {best_threshold if 'best_threshold' in locals() else 'N/A'}
"""

if trading_results and 'best_threshold' in locals():
    best_trading = trading_results[best_threshold]
    summary_report += f"""
Total Return: {best_trading['total_return']:.4f}
Sharpe Ratio: {best_trading['sharpe_ratio']:.4f}
Max Drawdown: {best_trading['max_drawdown']:.4f}
Number of Trades: {best_trading['num_trades']}
Win Rate: {best_trading['win_rate']:.4f}
"""

summary_report += f"""

Files Saved:
- Model: {best_model_path}
- Scaler: {scaler_path}
- Features: {feature_info_path}
- Metrics: {metrics_path}
- Preprocessing: {preprocessing_path}

=== END SUMMARY ===
"""

print(summary_report)

# Save summary report
summary_path = os.path.join(advanced_model_dir, 'model_summary_v3.txt')
with open(summary_path, 'w') as f:
    f.write(summary_report)

print(f"\nSummary report saved to: {summary_path}")
print("\n🎉 State-of-the-art BTC prediction model creation completed successfully!")

