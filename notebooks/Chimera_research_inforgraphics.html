<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Chimera: A Post-Human Trading Model</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.plot.ly/plotly-2.27.0.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 400px;
            max-height: 50vh;
        }
        .flowchart-node {
            border: 2px solid #0A9396;
            background-color: #002c33;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(10, 147, 150, 0.2);
            transition: all 0.3s ease;
        }
        .flowchart-node:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(10, 147, 150, 0.4);
        }
        .flowchart-connector {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #94D2BD;
        }
        .flowchart-arrow::after {
            content: '→';
            font-size: 2.5rem;
            line-height: 1;
            margin: 0 1rem;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-200">

    <div class="container mx-auto p-4 md:p-8">

        <header class="text-center mb-16">
            <h1 class="text-5xl md:text-7xl font-black uppercase tracking-wider" style="color: #94D2BD;">Project Chimera</h1>
            <p class="text-xl md:text-2xl text-gray-400 mt-4 max-w-3xl mx-auto">A Post-Human Trading Blueprint. Analyzing the market not as a product of human psychology, but as a complex physical system.</p>
        </header>

        <section id="architecture" class="mb-20">
            <h2 class="text-3xl font-bold text-center mb-2" style="color: #E9D8A6;">A Three-Pillar System</h2>
            <p class="text-center text-gray-400 max-w-2xl mx-auto mb-10">Chimera's architecture is an ensemble of three distinct, non-conventional analytical pillars. A final trading signal emerges only from a consensus, ensuring a robust, multi-faceted decision process.</p>
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4">
                <div class="flex flex-col space-y-4">
                    <div class="flowchart-node">
                        <h3 class="font-bold text-lg" style="color: #94D2BD;">Pillar I: PSTA</h3>
                        <p class="text-sm text-gray-400">Phase-Space Trajectory</p>
                    </div>
                    <div class="flowchart-node">
                        <h3 class="font-bold text-lg" style="color: #94D2BD;">Pillar II: HIR</h3>
                        <p class="text-sm text-gray-400">Holographic Information Resonance</p>
                    </div>
                    <div class="flowchart-node">
                        <h3 class="font-bold text-lg" style="color: #94D2BD;">Pillar III: RSS</h3>
                        <p class="text-sm text-gray-400">Reflexive Swarm Simulation</p>
                    </div>
                </div>
                <div class="flowchart-connector transform md:transform-none -rotate-90 md:rotate-0">
                    <span class="flowchart-arrow"></span>
                </div>
                <div class="flowchart-node" style="border-color: #EE9B00;">
                    <h3 class="font-bold text-xl" style="color: #EE9B00;">Signal Engine</h3>
                    <p class="text-gray-300">Generates trade signal if ≥ 2 pillars agree</p>
                </div>
            </div>
        </section>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-3">
                <div class="bg-gray-800 rounded-lg shadow-xl p-6">
                    <h3 class="text-2xl font-bold mb-4" style="color: #0A9396;">Pillar I: Phase-Space Trajectory Analysis (PSTA)</h3>
                    <p class="text-gray-400 mb-6">This pillar models the market's state as a point moving through a multi-dimensional "phase space," defined by principal components of market data, not standard indicators. The goal is to predict the trajectory of this point as it moves between stable "attractor" regions, signaling major state transitions.</p>
                    <div id="pstaChartContainer" class="chart-container"></div>
                    <p class="text-center text-sm text-gray-500 mt-2">Visualization of a state-vector trajectory (line) moving between two distinct market-state attractors (clusters) in a 3D phase space.</p>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg shadow-xl p-6">
                <h3 class="text-2xl font-bold mb-4" style="color: #0A9396;">Pillar II: Holographic Information Resonance (HIR)</h3>
                <p class="text-gray-400 mb-6">Based on the holographic principle, HIR posits that BTC's future state is encoded in its interaction patterns with other assets. We use wavelet analysis to detect breakdowns in phase relationships across different time scales, signaling a change in market dynamics.</p>
                <div class="chart-container" style="height: 300px; max-height: 40vh;">
                    <canvas id="hirChart"></canvas>
                </div>
                <p class="text-center text-sm text-gray-500 mt-2">Cross-asset coherence analysis. A sudden de-correlation or phase shift between BTC and other key assets like the VIX can precede significant moves.</p>
            </div>

            <div class="bg-gray-800 rounded-lg shadow-xl p-6">
                <h3 class="text-2xl font-bold mb-4" style="color: #0A9396;">Pillar III: Reflexive Swarm Simulation (RSS)</h3>
                <p class="text-gray-400 mb-6">This pillar models the market's micro-dynamics to predict emergent swarm behaviors like liquidity cascades. By tracking net liquidity flow and herding behavior across assets, it identifies conditions ripe for systemic, reflexive price moves.</p>
                <div class="chart-container" style="height: 300px; max-height: 40vh;">
                    <canvas id="rssChart"></canvas>
                </div>
                <p class="text-center text-sm text-gray-500 mt-2">Herding Index over time. A sharp drop indicates assets are moving in lockstep, a common precursor to market-wide reversals.</p>
            </div>
            
            <div class="bg-gray-800 rounded-lg shadow-xl p-6">
                <h3 class="text-2xl font-bold mb-4" style="color: #EE9B00;">Empirical Validation Framework</h3>
                <p class="text-gray-400 mb-6">Evaluation transcends simple accuracy, focusing on risk-adjusted returns. The primary metric is the Calmar Ratio, heavily penalizing large drawdowns, with secondary focus on downside deviation (Sortino) and predictive power (Information Coefficient).</p>
                <div class="chart-container" style="height: 300px; max-height: 40vh;">
                    <canvas id="evaluationChart"></canvas>
                </div>
                <p class="text-center text-sm text-gray-500 mt-2">Relative importance of key evaluation metrics, prioritizing capital preservation and risk-adjusted performance.</p>
            </div>
        </div>

        <section id="backtesting" class="my-20">
            <h2 class="text-3xl font-bold text-center mb-2" style="color: #E9D8A6;">A More Robust Backtesting Protocol</h2>
            <p class="text-center text-gray-400 max-w-2xl mx-auto mb-10">To ensure viability, the model undergoes a rigorous walk-forward cross-validation, simulating a real-world retraining and deployment cycle across diverse market regimes.</p>
            <div class="flex flex-col items-center justify-center space-y-2 text-center text-sm">
                <div class="bg-gray-800 p-4 rounded-lg shadow-lg w-full md:w-3/4">
                    <span class="font-bold text-blue-300">Period 1:</span> Train on Data [0...100], Test on Data [101...125]
                </div>
                <div class="text-2xl text-gray-500">↓</div>
                <div class="bg-gray-800 p-4 rounded-lg shadow-lg w-full md:w-3/4">
                    <span class="font-bold text-blue-300">Period 2:</span> Train on Data [26...125], Test on Data [126...150]
                </div>
                <div class="text-2xl text-gray-500">↓</div>
                <div class="bg-gray-800 p-4 rounded-lg shadow-lg w-full md:w-3/4">
                    <span class="font-bold text-blue-300">Period 3:</span> Train on Data [51...150], Test on Data [151...175]
                </div>
                <p class="text-gray-500 pt-4">This rolling window approach prevents lookahead bias and ensures the model adapts to evolving market dynamics.</p>
            </div>
        </section>

        <footer class="text-center pt-8 border-t border-gray-700">
            <p class="text-gray-500">This infographic represents the conceptual framework of Project Chimera. All data is illustrative. Performance is not guaranteed.</p>
            <p class="text-xs text-gray-600 mt-2">Powered by a complex systems approach to quantitative trading.</p>
        </footer>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const brilliantBlues = {
                darkBlue: '#005F73',
                teal: '#0A9396',
                lightTeal: '#94D2BD',
                lightYellow: '#E9D8A6',
                gold: '#EE9B00',
                orange: '#CA6702',
                darkOrange: '#BB3E03',
                red: '#AE2012',
                darkRed: '#9B2226'
            };

            const chartLabelColor = 'rgba(229, 231, 235, 0.7)';
            const gridColor = 'rgba(255, 255, 255, 0.1)';
            
            function wrapLabels(label) {
                if (typeof label === 'string' && label.length > 16) {
                    const words = label.split(' ');
                    const lines = [];
                    let currentLine = '';
                    for (const word of words) {
                        if ((currentLine + word).length > 16) {
                            lines.push(currentLine.trim());
                            currentLine = '';
                        }
                        currentLine += word + ' ';
                    }
                    lines.push(currentLine.trim());
                    return lines;
                }
                return label;
            }

            const tooltipTitleCallback = (tooltipItems) => {
                const item = tooltipItems[0];
                let label = item.chart.data.labels[item.dataIndex];
                if (Array.isArray(label)) {
                    return label.join(' ');
                }
                return label;
            };

            const commonChartOptions = {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { color: chartLabelColor },
                        grid: { color: gridColor }
                    },
                    x: {
                        ticks: { color: chartLabelColor },
                        grid: { color: gridColor }
                    }
                },
                plugins: {
                    legend: {
                        labels: { color: chartLabelColor }
                    },
                    tooltip: {
                        callbacks: {
                            title: tooltipTitleCallback
                        }
                    }
                }
            };
            
            function createPstaChart() {
                const trace1 = {
                    x: Array.from({length: 50}, () => Math.random() * 50 - 25),
                    y: Array.from({length: 50}, () => Math.random() * 50 - 25),
                    z: Array.from({length: 50}, () => Math.random() * 50 - 25),
                    mode: 'markers',
                    type: 'scatter3d',
                    marker: {
                        color: brilliantBlues.teal,
                        size: 5,
                        opacity: 0.6
                    },
                    name: 'Attractor 1'
                };
                
                const trace2 = {
                    x: Array.from({length: 50}, () => Math.random() * 50 + 25),
                    y: Array.from({length: 50}, () => Math.random() * 50 + 25),
                    z: Array.from({length: 50}, () => Math.random() * 50 + 25),
                    mode: 'markers',
                    type: 'scatter3d',
                    marker: {
                        color: brilliantBlues.orange,
                        size: 5,
                        opacity: 0.6
                    },
                    name: 'Attractor 2'
                };

                const trajectoryX = [0, 5, 15, 20, 28, 35, 45, 50];
                const trajectoryY = [5, 10, 12, 22, 25, 32, 40, 48];
                const trajectoryZ = [2, 8, 18, 25, 29, 38, 42, 49];

                const trace3 = {
                    x: trajectoryX,
                    y: trajectoryY,
                    z: trajectoryZ,
                    mode: 'lines',
                    type: 'scatter3d',
                    line: {
                        color: brilliantBlues.lightYellow,
                        width: 4
                    },
                    name: 'State Trajectory'
                };

                const layout = {
                    title: {
                        text: 'Market State Trajectory',
                        font: { color: brilliantBlues.lightTeal }
                    },
                    showlegend: true,
                    legend: { font: { color: chartLabelColor } },
                    autosize: true,
                    paper_bgcolor: 'rgba(0,0,0,0)',
                    plot_bgcolor: 'rgba(0,0,0,0)',
                    scene: {
                        xaxis: { title: 'PCA 1', titlefont: {color: chartLabelColor}, tickfont: {color: chartLabelColor}, gridcolor: gridColor },
                        yaxis: { title: 'PCA 2', titlefont: {color: chartLabelColor}, tickfont: {color: chartLabelColor}, gridcolor: gridColor },
                        zaxis: { title: 'PCA 3', titlefont: {color: chartLabelColor}, tickfont: {color: chartLabelColor}, gridcolor: gridColor },
                    },
                    margin: { l: 0, r: 0, b: 0, t: 40 }
                };

                Plotly.newPlot('pstaChartContainer', [trace1, trace2, trace3], layout, {responsive: true});
            }

            function createHirChart() {
                const ctx = document.getElementById('hirChart').getContext('2d');
                const labels = Array.from({length: 30}, (_, i) => `T-${30-i}`);
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'BTC Price (Normalized)',
                            data: Array.from({length: 30}, () => Math.random()),
                            borderColor: brilliantBlues.lightTeal,
                            backgroundColor: 'transparent',
                            tension: 0.4
                        }, {
                            label: 'VIX (Normalized)',
                            data: Array.from({length: 30}, () => Math.random()),
                            borderColor: brilliantBlues.red,
                            backgroundColor: 'transparent',
                            tension: 0.4,
                            borderDash: [5, 5]
                        }]
                    },
                    options: {
                        ...commonChartOptions,
                        scales: {
                           y: { ...commonChartOptions.scales.y, beginAtZero: false },
                           x: { ...commonChartOptions.scales.x }
                        }
                    }
                });
            }

            function createRssChart() {
                const ctx = document.getElementById('rssChart').getContext('2d');
                const labels = Array.from({length: 30}, (_, i) => `T-${30-i}`);
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Herding Index',
                            data: [0.8, 0.82, 0.78, 0.85, 0.8, 0.75, 0.7, 0.65, 0.5, 0.4, 0.3, 0.25, 0.2, 0.18, 0.22, 0.3, 0.45, 0.55, 0.6, 0.7, 0.75, 0.8, 0.79, 0.81, 0.83, 0.8, 0.77, 0.76, 0.78, 0.8],
                            borderColor: brilliantBlues.gold,
                            backgroundColor: 'rgba(238, 155, 0, 0.2)',
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        ...commonChartOptions,
                        scales: {
                           y: { ...commonChartOptions.scales.y, beginAtZero: false, max: 1 },
                           x: { ...commonChartOptions.scales.x }
                        }
                    }
                });
            }

            function createEvaluationChart() {
                const ctx = document.getElementById('evaluationChart').getContext('2d');
                const rawLabels = ['Calmar Ratio', 'Sortino Ratio', 'Information Coefficient', 'Max Drawdown', 'Profit Factor'];
                const labels = rawLabels.map(wrapLabels);
                new Chart(ctx, {
                    type: 'radar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Metric Importance',
                            data: [10, 8, 7, 9, 6],
                            backgroundColor: 'rgba(148, 210, 189, 0.2)',
                            borderColor: brilliantBlues.lightTeal,
                            pointBackgroundColor: brilliantBlues.lightTeal,
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            tooltip: {
                                callbacks: {
                                    title: tooltipTitleCallback
                                }
                            }
                        },
                        scales: {
                            r: {
                                angleLines: { color: gridColor },
                                grid: { color: gridColor },
                                pointLabels: { color: chartLabelColor, font: { size: 12 } },
                                ticks: {
                                    color: chartLabelColor,
                                    backdropColor: 'rgba(0,0,0,0)'
                                }
                            }
                        }
                    }
                });
            }

            createPstaChart();
            createHirChart();
            createRssChart();
            createEvaluationChart();
        });
    </script>
</body>
</html>
