import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from loguru import logger

# Add project root to sys.path
# This is a common pattern to make project modules importable in notebooks
module_path = os.path.abspath(os.path.join('..'))
if module_path not in sys.path:
    sys.path.append(module_path)

# Import necessary components from our application
from app.services.backtesting.data_fetcher import BinanceDataFetcher
from app.services.models.xgboost_strategy import xgboost_strategy

# Scikit-learn and XGBoost
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

# Configure logger
logger.add("notebook_runtime.log", rotation="500 MB")

print("Setup complete. Modules imported and path configured.")

# Initialize the data fetcher
fetcher = BinanceDataFetcher()

# Define parameters
symbol = 'BTC/USDT'
timeframe = '1h'
days = 720
end_date = datetime.utcnow()
start_date = end_date - timedelta(days=days)

# Fetch the data
logger.info(f"Fetching data for {symbol} from {start_date} to {end_date}")
history_df = fetcher.fetch(symbol, timeframe, start_date, end_date)

# Display the first few rows and shape of the dataframe
print("Data fetched successfully.")
print("Shape of the dataframe:", history_df.shape)
history_df.head()

"""
Alien BTC Strategy Prototype

This prototype implements a scaffolding for the "alien" trading blueprint described earlier.
It is intentionally modular: feature engineering (entropy, permutation entropy, latent embeddings),
state discovery, manifold-aware classifier, simple meta-controller (bandit-style), and a
backtesting harness with multiverse wartesting and adversarial injection.

USAGE
- If you're running inside the same environment as your original notebook and `history_df` is already
  in memory, the Loader will use it. Otherwise, provide a CSV path to OHLCV data with a DatetimeIndex.

Note: This is a prototype. Some advanced components (persistent homology, full RL meta-controller)
are provided as clear placeholders/stubs so you can plug in more specialized libraries.
"""

import os
import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import precision_score, recall_score
from xgboost import XGBClassifier
import joblib
import random
import math

# ----------------------------- Loader ---------------------------------

def load_history_df(csv_path=None):
    """Try to reuse an in-memory `history_df` if present (from your original notebook).
    Otherwise load from CSV. The dataframe must have columns: open, high, low, close, volume
    and a DatetimeIndex.
    """
    try:
        # Attempt to access an already-loaded variable in the environment
        from IPython import get_ipython
        ip = get_ipython()
        if ip and 'history_df' in ip.user_ns:
            print("Found in-memory history_df; using it.")
            return ip.user_ns['history_df'].copy()
    except Exception:
        pass

    if csv_path is None:
        raise ValueError("No history_df found and no csv_path provided. Provide a path to OHLCV CSV.")

    df = pd.read_csv(csv_path, parse_dates=[0], index_col=0)
    df = df.sort_index()
    return df

def shannon_entropy(series, base=2):
    vals, counts = np.unique(series, return_counts=True)
    probs = counts / counts.sum()
    ent = -(probs * np.log(probs) / np.log(base)).sum()
    return ent


def permutation_entropy(time_series, order=3, delay=1):
    """Simple permutation entropy implementation."""
    x = np.array(time_series)
    n = len(x)
    if n < order:
        return np.nan
    perms = {}
    for i in range(n - delay * (order - 1)):
        window = x[i:(i + delay * order):delay]
        ranks = tuple(np.argsort(window))
        perms[ranks] = perms.get(ranks, 0) + 1
    probs = np.array(list(perms.values())) / sum(perms.values())
    return -(probs * np.log(probs)).sum()


def rolling_entropy_features(df, window=64):
    return pd.DataFrame({
        'shannon_entropy': df['close'].rolling(window).apply(lambda x: shannon_entropy(np.round(100 * x.diff().fillna(0), 6))),
        'perm_entropy': df['close'].rolling(window).apply(lambda x: permutation_entropy(x.values, order=4, delay=1))
    })


def wick_imbalance(df):
    # Approximate orderbook imbalance from OHLC by comparing wick sizes
    upper_wick = df['high'] - df[['open', 'close']].max(axis=1)
    lower_wick = df[['open', 'close']].min(axis=1) - df['low']
    imbalance = (upper_wick - lower_wick) / (upper_wick + lower_wick + 1e-9)
    return imbalance


def make_rolling_autoencoder_embeddings(df, window=64, latent_dim=4):
    """Use an MLP autoencoder (scikit-learn) on rolling windows to produce latent embeddings.
    This is a fast, dependency-light stand-in for a neural autoencoder.
    Returns a DataFrame of latent dimensions aligned with the original index (NaN where not computable).
    """
    cols = ['open', 'high', 'low', 'close', 'volume']
    X = (df[cols].pct_change().fillna(0) + 1).rolling(window).apply(lambda x: x.values.flatten())

    # Build training matrix where rolling windows are complete
    windows = []
    idxs = []
    for i in range(window, len(df) + 1):
        win = df.iloc[i-window:i][cols].pct_change().fillna(0).values.flatten()
        windows.append(win)
        idxs.append(df.index[i-1])
    if len(windows) == 0:
        return pd.DataFrame(index=df.index)

    Xmat = np.vstack(windows)
    scaler = StandardScaler()
    Xs = scaler.fit_transform(Xmat)

    # Simple bottleneck autoencoder implemented as two MLPRegressors (encoder + decoder approximation)
    encoder = PCA(n_components=latent_dim)
    Z = encoder.fit_transform(Xs)

    latent_df = pd.DataFrame(Z, index=idxs, columns=[f'latent_{i}' for i in range(latent_dim)])
    return latent_df.reindex(df.index)

# ---------------------- State Discovery -------------------------------

def assemble_features(df):
    f = pd.DataFrame(index=df.index)
    f['close'] = df['close']
    f['returns'] = df['close'].pct_change().fillna(0)
    f['volatility'] = f['returns'].rolling(21).std()
    f['wick_imbalance'] = wick_imbalance(df)
    ent = rolling_entropy_features(df, window=64)
    f = f.join(ent)
    latent = make_rolling_autoencoder_embeddings(df, window=64, latent_dim=6)
    f = f.join(latent)
    # Simple anomaly score
    iso = IsolationForest(n_estimators=100, contamination=0.01, random_state=42)
    # Use numeric columns for isolation forest
    num_cols = f.select_dtypes(include=[np.number]).columns
    # Fill nans for fitting
    X = f[num_cols].fillna(0).values
    iso.fit(X)
    f['anomaly_score'] = -iso.score_samples(X)  # higher means more anomalous
    return f

# ---------------------- State Labels (Targets) ------------------------

def label_states(f):
    """Create a 3-state label: attractor (stable), transition (trend), noise (no-trade).
    This is heuristic and meant for training the state classifier.
    """
    labels = pd.Series(index=f.index, dtype='int')
    # Heuristics:
    # - Noise: high entropy and high anomaly_score
    # - Attractor: low volatility, low entropy
    # - Transition: otherwise
    ent = f['perm_entropy'].fillna(f['perm_entropy'].median())
    vol = f['volatility'].fillna(f['volatility'].median())
    anom = f['anomaly_score'].fillna(0)

    noise_mask = (ent > ent.quantile(0.75)) & (anom > anom.quantile(0.75))
    attractor_mask = (vol < vol.quantile(0.25)) & (ent < ent.quantile(0.25))
    labels[noise_mask] = 0
    labels[attractor_mask] = 1
    labels.fillna(2, inplace=True)  # transition
    return labels.astype(int)

# ---------------------- Manifold-aware classifier --------------------

def train_state_classifier(F, labels):
    features = F.select_dtypes(include=[np.number]).fillna(0)
    model = XGBClassifier(use_label_encoder=False, eval_metric='mlogloss', n_jobs=4)
    tscv = TimeSeriesSplit(n_splits=5)
    # Simple training on entire set; you should tune with proper CV and walk-forward tests
    model.fit(features, labels)
    return model

# ---------------------- Meta-controller (Bandit stub) -----------------

class Exp3MetaController:
    """A lightweight adversarial bandit-based meta-controller to choose between strategies.
    Treats each sub-strategy as an arm; updates weights based on PnL signals.
    This avoids heavy RL dependencies while providing adaptivity.
    """
    def __init__(self, n_arms, gamma=0.07):
        self.n = n_arms
        self.gamma = gamma
        self.weights = np.ones(n_arms)

    def get_probabilities(self):
        w = self.weights
        probs = (1 - self.gamma) * (w / w.sum()) + self.gamma / self.n
        return probs

    def sample_arm(self):
        p = self.get_probabilities()
        return np.random.choice(self.n, p=p)

    def update(self, arm, reward):
        p = self.get_probabilities()[arm]
        est_reward = reward / (p + 1e-12)
        self.weights[arm] *= np.exp((self.gamma * est_reward) / self.n)

# ---------------------- Backtester ----------------------------------

def simple_vectorized_backtest(df, signals, position_sizing, fee=0.00075):
    """signals: Series of signed desired positions in fraction of capital (-1..1)
    position_sizing: function(index, confidence) -> size
    Returns a DataFrame with equity curve and metrics
    """
    close = df['close'].loc[signals.index]
    returns = close.pct_change().fillna(0)
    positions = signals.shift(1).fillna(0)  # assume act on previous signal
    pnl = positions * returns
    # fees approximation: when position changes, apply fee proportional to abs(change)
    pos_change = positions.diff().abs().fillna(0)
    fees = pos_change * fee
    net_pnl = pnl - fees
    equity = (1 + net_pnl).cumprod()
    result = pd.DataFrame({
        'returns': returns,
        'positions': positions,
        'pnl': pnl,
        'fees': fees,
        'net_pnl': net_pnl,
        'equity': equity
    }, index=signals.index)
    return result

# ---------------------- Multiverse / Adversarial tests ----------------

def multiverse_warp(df, n_variants=8, max_shift=100):
    """Produce variants of the historical series by random non-overlapping segment shuffles.
    This keeps local structures but breaks long-sequence artifacts.
    """
    variants = []
    n = len(df)
    seg_len = max(1, n // 20)
    indices = list(range(0, n, seg_len))
    for v in range(n_variants):
        perm = indices.copy()
        random.shuffle(perm)
        parts = []
        for i in perm:
            parts.append(df.iloc[i:i+seg_len])
        variants.append(pd.concat(parts).reset_index(drop=False).set_index('index'))
    return variants


def inject_adversarial_events(df, n_events=3):
    df2 = df.copy()
    n = len(df2)
    for _ in range(n_events):
        t = random.randint(50, n-50)
        magnitude = 0.05 + random.random() * 0.3
        # create a flash crash + recovery over 3 bars
        df2.iloc[t]['close'] *= (1 - magnitude)
        if t+1 < n:
            df2.iloc[t+1]['close'] *= (1 - magnitude/2)
        if t+2 < n:
            df2.iloc[t+2]['close'] *= (1 + magnitude/2)
    return df2

# ---------------------- Evaluation metrics ---------------------------

def profit_adjusted_drawdown_ratio(equity_series):
    eq = equity_series.dropna()
    total_return = eq.iloc[-1] / eq.iloc[0] - 1
    running_max = eq.cummax()
    drawdowns = (eq - running_max) / running_max
    max_dd = drawdowns.min()
    if max_dd == 0:
        return np.nan
    paddr = total_return / abs(max_dd)
    return paddr

# ---------------------- End-to-end Pipeline --------------------------

def build_and_backtest_pipeline(history_df=None, csv_path=None):
    df = load_history_df(csv_path) if history_df is None else history_df
    F = assemble_features(df)
    labels = label_states(F)
    clf = train_state_classifier(F, labels)
  
    # Predict states and produce simple signal: in attractor (1) trend-follow; in transition (2) momentum; in noise (0) flat
    X = F.select_dtypes(include=[np.number]).fillna(0)
    states = pd.Series(clf.predict(X), index=F.index, name='state')

    # Build signals
    signals = pd.Series(0.0, index=F.index)
    # Attractor: mean-revert small positions
    signals[states == 1] = -0.5 * np.sign(F['returns'][states == 1].rolling(3).mean().fillna(0))
    # Transition: trend follow
    signals[states == 2] = 1.0 * np.sign(F['returns'][states == 2].rolling(5).mean().fillna(0))
    # Noise: 0

    # Backtest on base and multiverse
    base_bt = simple_vectorized_backtest(df, signals)

    multiverse_results = []
    variants = multiverse_warp(df, n_variants=6)
    for vdf in variants:
        try:
            VF = assemble_features(vdf)
            VX = VF.select_dtypes(include=[np.number]).fillna(0)
            vstates = pd.Series(clf.predict(VX), index=VF.index)
            vsignals = pd.Series(0.0, index=VF.index)
            vsignals[vstates == 1] = -0.5 * np.sign(VF['returns'][vstates == 1].rolling(3).mean().fillna(0))
            vsignals[vstates == 2] = 1.0 * np.sign(VF['returns'][vstates == 2].rolling(5).mean().fillna(0))
            vbt = simple_vectorized_backtest(vdf, vsignals)
            multiverse_results.append(vbt)
        except Exception as e:
            print("Variant failed:", e)

    # Adversarial injection test
    adv = inject_adversarial_events(df, n_events=4)
    AF = assemble_features(adv)
    AX = AF.select_dtypes(include=[np.number]).fillna(0)
    astates = pd.Series(clf.predict(AX), index=AF.index)
    asignals = pd.Series(0.0, index=AF.index)
    asignals[astates == 1] = -0.5 * np.sign(AF['returns'][astates == 1].rolling(3).mean().fillna(0))
    asignals[astates == 2] = 1.0 * np.sign(AF['returns'][astates == 2].rolling(5).mean().fillna(0))
    adv_bt = simple_vectorized_backtest(adv, asignals)

    # Metrics
    metrics = {
        'base_PADDR': profit_adjusted_drawdown_ratio(base_bt['equity']),
        'adv_PADDR': profit_adjusted_drawdown_ratio(adv_bt['equity']),
        'multiverse_count': len(multiverse_results)
    }

    artifacts = {
        'classifier': clf,
        'features_snapshot': F,
        'labels_snapshot': labels,
        'base_backtest': base_bt,
        'multiverse_backtests': multiverse_results,
        'adversarial_backtest': adv_bt,
        'metrics': metrics
    }
    return artifacts

hist = None
try:
    hist = load_history_df("")
except Exception as e:
    print('Loader error:', e)
    raise

art = build_and_backtest_pipeline(history_df=hist)

