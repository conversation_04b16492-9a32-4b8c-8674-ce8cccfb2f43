{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from dotenv import load_dotenv\n", "from alpaca.data.historical import CryptoHistoricalDataClient\n", "from alpaca.data.requests import CryptoBarsRequest\n", "from alpaca.data.timeframe import TimeFrame\n", "from datetime import datetime\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Load environment variables from the .env file\n", "load_dotenv()\n", "access_key = os.getenv(\"ALPACA_DEV_ACCESS_KEY\")\n", "secret_key = os.getenv(\"ALPACA_DEV_SECRET_KEY\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### History"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>trade_count</th>\n", "      <th>vwap</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>timestamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">BTC/USD</th>\n", "      <th>2022-07-01 00:00:00+00:00</th>\n", "      <td>19935.53</td>\n", "      <td>20896.36</td>\n", "      <td>19638.70</td>\n", "      <td>20268.46</td>\n", "      <td>1005.036456</td>\n", "      <td>16265.0</td>\n", "      <td>20241.098313</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 01:00:00+00:00</th>\n", "      <td>20265.70</td>\n", "      <td>20467.94</td>\n", "      <td>20225.50</td>\n", "      <td>20386.73</td>\n", "      <td>723.770069</td>\n", "      <td>10530.0</td>\n", "      <td>20331.109787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 02:00:00+00:00</th>\n", "      <td>20390.07</td>\n", "      <td>20518.34</td>\n", "      <td>20236.41</td>\n", "      <td>20265.67</td>\n", "      <td>429.473926</td>\n", "      <td>5882.0</td>\n", "      <td>20353.703820</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 03:00:00+00:00</th>\n", "      <td>20265.66</td>\n", "      <td>20284.88</td>\n", "      <td>19660.54</td>\n", "      <td>19696.85</td>\n", "      <td>529.167195</td>\n", "      <td>6473.0</td>\n", "      <td>20034.535558</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 04:00:00+00:00</th>\n", "      <td>19697.25</td>\n", "      <td>19726.26</td>\n", "      <td>19347.66</td>\n", "      <td>19382.09</td>\n", "      <td>565.767071</td>\n", "      <td>7816.0</td>\n", "      <td>19549.198647</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">ETH/USD</th>\n", "      <th>2022-08-31 20:00:00+00:00</th>\n", "      <td>1575.90</td>\n", "      <td>1578.67</td>\n", "      <td>1564.41</td>\n", "      <td>1570.98</td>\n", "      <td>844.880890</td>\n", "      <td>837.0</td>\n", "      <td>1570.268458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-31 21:00:00+00:00</th>\n", "      <td>1570.34</td>\n", "      <td>1582.01</td>\n", "      <td>1564.87</td>\n", "      <td>1579.82</td>\n", "      <td>481.104350</td>\n", "      <td>511.0</td>\n", "      <td>1575.182105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-31 22:00:00+00:00</th>\n", "      <td>1579.69</td>\n", "      <td>1584.55</td>\n", "      <td>1550.98</td>\n", "      <td>1568.20</td>\n", "      <td>1015.364560</td>\n", "      <td>1091.0</td>\n", "      <td>1564.808164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-08-31 23:00:00+00:00</th>\n", "      <td>1568.93</td>\n", "      <td>1568.93</td>\n", "      <td>1542.54</td>\n", "      <td>1554.49</td>\n", "      <td>1135.621380</td>\n", "      <td>921.0</td>\n", "      <td>1555.434322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-01 00:00:00+00:00</th>\n", "      <td>1554.41</td>\n", "      <td>1566.40</td>\n", "      <td>1533.65</td>\n", "      <td>1551.53</td>\n", "      <td>1829.991930</td>\n", "      <td>1893.0</td>\n", "      <td>1550.225386</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2978 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                       open      high       low     close  \\\n", "symbol  timestamp                                                           \n", "BTC/USD 2022-07-01 00:00:00+00:00  19935.53  20896.36  19638.70  20268.46   \n", "        2022-07-01 01:00:00+00:00  20265.70  20467.94  20225.50  20386.73   \n", "        2022-07-01 02:00:00+00:00  20390.07  20518.34  20236.41  20265.67   \n", "        2022-07-01 03:00:00+00:00  20265.66  20284.88  19660.54  19696.85   \n", "        2022-07-01 04:00:00+00:00  19697.25  19726.26  19347.66  19382.09   \n", "...                                     ...       ...       ...       ...   \n", "ETH/USD 2022-08-31 20:00:00+00:00   1575.90   1578.67   1564.41   1570.98   \n", "        2022-08-31 21:00:00+00:00   1570.34   1582.01   1564.87   1579.82   \n", "        2022-08-31 22:00:00+00:00   1579.69   1584.55   1550.98   1568.20   \n", "        2022-08-31 23:00:00+00:00   1568.93   1568.93   1542.54   1554.49   \n", "        2022-09-01 00:00:00+00:00   1554.41   1566.40   1533.65   1551.53   \n", "\n", "                                        volume  trade_count          vwap  \n", "symbol  timestamp                                                          \n", "BTC/USD 2022-07-01 00:00:00+00:00  1005.036456      16265.0  20241.098313  \n", "        2022-07-01 01:00:00+00:00   723.770069      10530.0  20331.109787  \n", "        2022-07-01 02:00:00+00:00   429.473926       5882.0  20353.703820  \n", "        2022-07-01 03:00:00+00:00   529.167195       6473.0  20034.535558  \n", "        2022-07-01 04:00:00+00:00   565.767071       7816.0  19549.198647  \n", "...                                        ...          ...           ...  \n", "ETH/USD 2022-08-31 20:00:00+00:00   844.880890        837.0   1570.268458  \n", "        2022-08-31 21:00:00+00:00   481.104350        511.0   1575.182105  \n", "        2022-08-31 22:00:00+00:00  1015.364560       1091.0   1564.808164  \n", "        2022-08-31 23:00:00+00:00  1135.621380        921.0   1555.434322  \n", "        2022-09-01 00:00:00+00:00  1829.991930       1893.0   1550.225386  \n", "\n", "[2978 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# no keys required.\n", "crypto_client = CryptoHistoricalDataClient()\n", "# keys required\n", "stock_client = CryptoHistoricalDataClient(access_key, secret_key)\n", "request_params = CryptoBarsRequest(\n", "                        symbol_or_symbols=[\"BTC/USD\", \"ETH/USD\"],\n", "                        timeframe=TimeFrame.Hour,\n", "                        start=datetime(2022, 7, 1),\n", "                        end=datetime(2022, 9, 1)\n", "                 )\n", "bars = crypto_client.get_crypto_bars(request_params)\n", "\n", "# convert to dataframe\n", "bars.df\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pipreqs\n", "  Downloading pipreqs-0.5.0-py3-none-any.whl.metadata (7.9 kB)\n", "Collecting docopt==0.6.2 (from pipreqs)\n", "  Downloading docopt-0.6.2.tar.gz (25 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hCollecting ipython==8.12.3 (from pipreqs)\n", "  Downloading ipython-8.12.3-py3-none-any.whl.metadata (5.7 kB)\n", "Collecting nbconvert<8.0.0,>=7.11.0 (from pipreqs)\n", "  Downloading nbconvert-7.16.4-py3-none-any.whl.metadata (8.5 kB)\n", "Collecting yarg==0.1.9 (from pipreqs)\n", "  Downloading yarg-0.1.9-py2.py3-none-any.whl.metadata (4.6 kB)\n", "Collecting backcall (from ipython==8.12.3->pipreqs)\n", "  Downloading backcall-0.2.0-py2.py3-none-any.whl.metadata (2.0 kB)\n", "Requirement already satisfied: decorator in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (5.1.1)\n", "Requirement already satisfied: jedi>=0.16 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (0.19.1)\n", "Requirement already satisfied: matplotlib-inline in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (0.1.7)\n", "Requirement already satisfied: pickleshare in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (0.7.5)\n", "Requirement already satisfied: prompt-toolkit!=3.0.37,<3.1.0,>=3.0.30 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (3.0.47)\n", "Requirement already satisfied: pygments>=2.4.0 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (2.18.0)\n", "Requirement already satisfied: stack-data in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (0.6.2)\n", "Requirement already satisfied: traitlets>=5 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (5.14.3)\n", "Requirement already satisfied: pexpect>4.3 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from ipython==8.12.3->pipreqs) (4.9.0)\n", "Requirement already satisfied: requests in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from yarg==0.1.9->pipreqs) (2.32.3)\n", "Collecting beautifulsoup4 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached beautifulsoup4-4.12.3-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting bleach!=5.0.0 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading bleach-6.1.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting defusedxml (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading defusedxml-0.7.1-py2.py3-none-any.whl.metadata (32 kB)\n", "Collecting jinja2>=3.0 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached jinja2-3.1.4-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: jupyter-core>=4.7 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from nbconvert<8.0.0,>=7.11.0->pipreqs) (5.7.2)\n", "Collecting jupyterlab-pygments (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading jupyterlab_pygments-0.3.0-py3-none-any.whl.metadata (4.4 kB)\n", "Collecting markupsafe>=2.0 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached MarkupSafe-2.1.5-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.0 kB)\n", "Collecting mistune<4,>=2.0.3 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading mistune-3.0.2-py3-none-any.whl.metadata (1.7 kB)\n", "Collecting nbclient>=0.5.0 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading nbclient-0.10.0-py3-none-any.whl.metadata (7.8 kB)\n", "Collecting nbformat>=5.7 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading nbformat-5.10.4-py3-none-any.whl.metadata (3.6 kB)\n", "Requirement already satisfied: packaging in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from nbconvert<8.0.0,>=7.11.0->pipreqs) (24.1)\n", "Collecting pandocfilters>=1.4.1 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading pandocfilters-1.5.1-py2.py3-none-any.whl.metadata (9.0 kB)\n", "Collecting tinycss2 (from nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading tinycss2-1.3.0-py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: six>=1.9.0 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from bleach!=5.0.0->nbconvert<8.0.0,>=7.11.0->pipreqs) (1.16.0)\n", "Collecting webencodings (from bleach!=5.0.0->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading webencodings-0.5.1-py2.py3-none-any.whl.metadata (2.1 kB)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.3 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from jedi>=0.16->ipython==8.12.3->pipreqs) (0.8.4)\n", "Requirement already satisfied: platformdirs>=2.5 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from jupyter-core>=4.7->nbconvert<8.0.0,>=7.11.0->pipreqs) (4.3.6)\n", "Requirement already satisfied: jupyter-client>=6.1.12 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from nbclient>=0.5.0->nbconvert<8.0.0,>=7.11.0->pipreqs) (8.6.3)\n", "Collecting fastjsonschema>=2.15 (from nbformat>=5.7->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Downloading fastjsonschema-2.20.0-py3-none-any.whl.metadata (2.1 kB)\n", "Collecting jsonschema>=2.6 (from nbformat>=5.7->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached jsonschema-4.23.0-py3-none-any.whl.metadata (7.9 kB)\n", "Requirement already satisfied: ptyprocess>=0.5 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from pexpect>4.3->ipython==8.12.3->pipreqs) (0.7.0)\n", "Requirement already satisfied: wcwidth in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from prompt-toolkit!=3.0.37,<3.1.0,>=3.0.30->ipython==8.12.3->pipreqs) (0.2.13)\n", "Collecting soupsieve>1.2 (from beautifulsoup4->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached soupsieve-2.6-py3-none-any.whl.metadata (4.6 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from requests->yarg==0.1.9->pipreqs) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from requests->yarg==0.1.9->pipreqs) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from requests->yarg==0.1.9->pipreqs) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from requests->yarg==0.1.9->pipreqs) (2024.8.30)\n", "Requirement already satisfied: executing>=1.2.0 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from stack-data->ipython==8.12.3->pipreqs) (2.1.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from stack-data->ipython==8.12.3->pipreqs) (2.4.1)\n", "Requirement already satisfied: pure-eval in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from stack-data->ipython==8.12.3->pipreqs) (0.2.3)\n", "Collecting attrs>=22.2.0 (from jsonschema>=2.6->nbformat>=5.7->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached attrs-24.2.0-py3-none-any.whl.metadata (11 kB)\n", "Collecting jsonschema-specifications>=2023.03.6 (from jsonschema>=2.6->nbformat>=5.7->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached jsonschema_specifications-2023.12.1-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting referencing>=0.28.4 (from jsonschema>=2.6->nbformat>=5.7->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached referencing-0.35.1-py3-none-any.whl.metadata (2.8 kB)\n", "Collecting rpds-py>=0.7.1 (from jsonschema>=2.6->nbformat>=5.7->nbconvert<8.0.0,>=7.11.0->pipreqs)\n", "  Using cached rpds_py-0.20.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.2 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from jupyter-client>=6.1.12->nbclient>=0.5.0->nbconvert<8.0.0,>=7.11.0->pipreqs) (2.9.0)\n", "Requirement already satisfied: pyzmq>=23.0 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from jupyter-client>=6.1.12->nbclient>=0.5.0->nbconvert<8.0.0,>=7.11.0->pipreqs) (26.2.0)\n", "Requirement already satisfied: tornado>=6.2 in /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages (from jupyter-client>=6.1.12->nbclient>=0.5.0->nbconvert<8.0.0,>=7.11.0->pipreqs) (6.4.1)\n", "Downloading pipreqs-0.5.0-py3-none-any.whl (33 kB)\n", "Downloading ipython-8.12.3-py3-none-any.whl (798 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m798.3/798.3 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m-:--:--\u001b[0m\n", "\u001b[?25hDownloading yarg-0.1.9-py2.py3-none-any.whl (19 kB)\n", "Downloading nbconvert-7.16.4-py3-none-any.whl (257 kB)\n", "Downloading bleach-6.1.0-py3-none-any.whl (162 kB)\n", "Using cached jinja2-3.1.4-py3-none-any.whl (133 kB)\n", "Using cached MarkupSafe-2.1.5-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (28 kB)\n", "Downloading mistune-3.0.2-py3-none-any.whl (47 kB)\n", "Downloading nbclient-0.10.0-py3-none-any.whl (25 kB)\n", "Downloading nbformat-5.10.4-py3-none-any.whl (78 kB)\n", "Downloading pandocfilters-1.5.1-py2.py3-none-any.whl (8.7 kB)\n", "Downloading backcall-0.2.0-py2.py3-none-any.whl (11 kB)\n", "Using cached beautifulsoup4-4.12.3-py3-none-any.whl (147 kB)\n", "Downloading defusedxml-0.7.1-py2.py3-none-any.whl (25 kB)\n", "Downloading jupyterlab_pygments-0.3.0-py3-none-any.whl (15 kB)\n", "Downloading tinycss2-1.3.0-py3-none-any.whl (22 kB)\n", "Downloading fastjsonschema-2.20.0-py3-none-any.whl (23 kB)\n", "Using cached jsonschema-4.23.0-py3-none-any.whl (88 kB)\n", "Using cached soupsieve-2.6-py3-none-any.whl (36 kB)\n", "Downloading webencodings-0.5.1-py2.py3-none-any.whl (11 kB)\n", "Using cached attrs-24.2.0-py3-none-any.whl (63 kB)\n", "Using cached jsonschema_specifications-2023.12.1-py3-none-any.whl (18 kB)\n", "Using cached referencing-0.35.1-py3-none-any.whl (26 kB)\n", "Using cached rpds_py-0.20.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (357 kB)\n", "Building wheels for collected packages: docopt\n", "  Building wheel for docopt (setup.py) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for docopt: filename=docopt-0.6.2-py2.py3-none-any.whl size=13707 sha256=ccd38053358e4046603284ca212aa44e84379b8b40bb317cf51572be3b67563f\n", "  Stored in directory: /home/<USER>/.cache/pip/wheels/1a/bf/a1/4cee4f7678c68c5875ca89eaccf460593539805c3906722228\n", "Successfully built docopt\n", "Installing collected packages: webencodings, fastjsonschema, docopt, backcall, tinycss2, soupsieve, rpds-py, pandocfilters, mistune, markupsafe, jupyterlab-pygments, defusedxml, bleach, attrs, yarg, referencing, jinja2, beautifulsoup4, jsonschema-specifications, ipython, jsonschema, nbformat, nbclient, nbconvert, pipreqs\n", "  Attempting uninstall: ipython\n", "    Found existing installation: ipython 8.27.0\n", "    Uninstalling ipython-8.27.0:\n", "      Successfully uninstalled ipython-8.27.0\n", "Successfully installed attrs-24.2.0 backcall-0.2.0 beautifulsoup4-4.12.3 bleach-6.1.0 defusedxml-0.7.1 docopt-0.6.2 fastjsonschema-2.20.0 ipython-8.12.3 jinja2-3.1.4 jsonschema-4.23.0 jsonschema-specifications-2023.12.1 jupyterlab-pygments-0.3.0 markupsafe-2.1.5 mistune-3.0.2 nbclient-0.10.0 nbconvert-7.16.4 nbformat-5.10.4 pandocfilters-1.5.1 pipreqs-0.5.0 referencing-0.35.1 rpds-py-0.20.0 soupsieve-2.6 tinycss2-1.3.0 webencodings-0.5.1 yarg-0.1.9\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install pipreqs\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pym<PERSON>o\n", "  Downloading pymongo-4.9.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (22 kB)\n", "Collecting dnspython<3.0.0,>=1.16.0 (from pymongo)\n", "  Downloading dnspython-2.6.1-py3-none-any.whl.metadata (5.8 kB)\n", "Downloading pymongo-4.9.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.9/1.9 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading dnspython-2.6.1-py3-none-any.whl (307 kB)\n", "Installing collected packages: dnspython, pymongo\n", "Successfully installed dnspython-2.6.1 pymongo-4.9.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install pymongo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Streaming"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                       open      high       low     close  \\\n", "symbol  timestamp                                                           \n", "BTC/USD 2022-07-01 00:00:00+00:00  19935.53  20896.36  19638.70  20268.46   \n", "        2022-07-01 01:00:00+00:00  20265.70  20467.94  20225.50  20386.73   \n", "        2022-07-01 02:00:00+00:00  20390.07  20518.34  20236.41  20265.67   \n", "        2022-07-01 03:00:00+00:00  20265.66  20284.88  19660.54  19696.85   \n", "        2022-07-01 04:00:00+00:00  19697.25  19726.26  19347.66  19382.09   \n", "...                                     ...       ...       ...       ...   \n", "ETH/USD 2022-08-31 20:00:00+00:00   1575.90   1578.67   1564.41   1570.98   \n", "        2022-08-31 21:00:00+00:00   1570.34   1582.01   1564.87   1579.82   \n", "        2022-08-31 22:00:00+00:00   1579.69   1584.55   1550.98   1568.20   \n", "        2022-08-31 23:00:00+00:00   1568.93   1568.93   1542.54   1554.49   \n", "        2022-09-01 00:00:00+00:00   1554.41   1566.40   1533.65   1551.53   \n", "\n", "                                        volume  trade_count          vwap  \n", "symbol  timestamp                                                          \n", "BTC/USD 2022-07-01 00:00:00+00:00  1005.036456      16265.0  20241.098313  \n", "        2022-07-01 01:00:00+00:00   723.770069      10530.0  20331.109787  \n", "        2022-07-01 02:00:00+00:00   429.473926       5882.0  20353.703820  \n", "        2022-07-01 03:00:00+00:00   529.167195       6473.0  20034.535558  \n", "        2022-07-01 04:00:00+00:00   565.767071       7816.0  19549.198647  \n", "...                                        ...          ...           ...  \n", "ETH/USD 2022-08-31 20:00:00+00:00   844.880890        837.0   1570.268458  \n", "        2022-08-31 21:00:00+00:00   481.104350        511.0   1575.182105  \n", "        2022-08-31 22:00:00+00:00  1015.364560       1091.0   1564.808164  \n", "        2022-08-31 23:00:00+00:00  1135.621380        921.0   1555.434322  \n", "        2022-09-01 00:00:00+00:00  1829.991930       1893.0   1550.225386  \n", "\n", "[2978 rows x 7 columns]\n"]}], "source": ["print(bars.df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-11-26 17:18:02.876\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:02.879\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 1, 643902, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93090.638 ask_size=0.79651 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:02.881\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:02.884\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 1, 644081, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93149.396 ask_size=1.6189 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:02.887\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:02.889\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 1, 644088, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93038.537 ask_size=0.810735 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:16.437\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:16.439\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 15, 967589, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93070.761 ask_size=1.605 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:16.440\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:16.441\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 15, 967642, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93169.4 ask_size=2.4287 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:16.442\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:16.443\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 15, 967657, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93106.5 ask_size=0.7976 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:23.626\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:23.626\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 22, 870249, tzinfo=datetime.timezone.utc) bid_price=93023.8 bid_size=0.80045 bid_exchange=None ask_price=93106.5 ask_size=0.7976 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:23.627\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:23.628\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 22, 870393, tzinfo=datetime.timezone.utc) bid_price=93023.8 bid_size=0.80045 bid_exchange=None ask_price=93138.108 ask_size=1.599 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:23.628\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:23.629\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 22, 870584, tzinfo=datetime.timezone.utc) bid_price=93023.8 bid_size=0.80045 bid_exchange=None ask_price=93151.78 ask_size=0.801734 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:24.841\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:24.842\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 24, 375961, tzinfo=datetime.timezone.utc) bid_price=93000.0 bid_size=0.007 bid_exchange=None ask_price=93151.78 ask_size=0.801734 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.122\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.123\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 24, 376274, tzinfo=datetime.timezone.utc) bid_price=93053.16 bid_size=0.803737 bid_exchange=None ask_price=93151.78 ask_size=0.801734 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.123\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.124\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 24, 376286, tzinfo=datetime.timezone.utc) bid_price=93053.16 bid_size=0.803737 bid_exchange=None ask_price=93180.168 ask_size=1.599 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.125\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.125\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 24, 376379, tzinfo=datetime.timezone.utc) bid_price=93064.46 bid_size=1.619 bid_exchange=None ask_price=93180.168 ask_size=1.599 ask_exchange=None conditions=None tape=None\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.126\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1m<class 'alpaca.data.models.quotes.Quote'>\u001b[0m\n", "\u001b[32m2024-11-26 17:18:25.127\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mquote_data_handler\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mReceived quote data: symbol='BTC/USD' timestamp=datetime.datetime(2024, 11, 26, 15, 18, 24, 376382, tzinfo=datetime.timezone.utc) bid_price=93064.46 bid_size=1.619 bid_exchange=None ask_price=93189.8 ask_size=0.79612 ask_exchange=None conditions=None tape=None\u001b[0m\n"]}], "source": ["import os\n", "from alpaca.data.live.crypto import CryptoDataStream\n", "from loguru import logger\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()\n", "# Initialize the CryptoDataStream\n", "crypto_stream = CryptoDataStream(api_key=access_key, secret_key=secret_key)\n", "\n", "# Define an asynchronous handler function to process the incoming data\n", "async def quote_data_handler(data):\n", "    logger.info(type(data))\n", "    logger.info(f\"Received quote data: {data}\")\n", "\n", "# Subscribe to trade data for BTC/USD\n", "crypto_stream.subscribe_quotes(quote_data_handler, \"BTC/USD\")\n", "\n", "# Run the data stream\n", "await crypto_stream.run()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install --upgrade alpaca-py"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: alpaca-py\n", "Version: 0.30.1\n", "Summary: The Official Python SDK for Alpaca APIs\n", "Home-page: https://github.com/alpacahq/alpaca-py\n", "Author: <PERSON><PERSON>\n", "Author-email: <EMAIL>\n", "License: Apache-2.0\n", "Location: /home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages\n", "Requires: msgpack, pandas, pydantic, requests, sseclient-py, websockets\n", "Required-by: \n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip show alpaca-py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "hedgehog-backend", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}