{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from dotenv import load_dotenv\n", "from alpaca.data.historical import CryptoHistoricalDataClient, StockHistoricalDataClient\n", "from alpaca.data.requests import CryptoBarsRequest, StockBarsRequest\n", "from alpaca.data.timeframe import TimeFrame\n", "from datetime import datetime\n", "from datetime import timedelta\n", "import os"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Variables"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["{   'account_blocked': <PERSON><PERSON><PERSON>,\n", "    'account_number': 'PA3AAEJXEX4N',\n", "    'accrued_fees': '0',\n", "    'buying_power': '126.76',\n", "    'cash': '126.76',\n", "    'created_at': datetime.datetime(2025, 2, 15, 14, 19, 29, 232125, tzinfo=TzInfo(UTC)),\n", "    'crypto_status': <AccountStatus.ACTIVE: 'ACTIVE'>,\n", "    'currency': 'USD',\n", "    'daytrade_count': 0,\n", "    'daytrading_buying_power': '0',\n", "    'equity': '199.83',\n", "    'id': UUID('96a1603e-94dc-44dd-8142-22beb5b0f1f8'),\n", "    'initial_margin': '0',\n", "    'last_equity': '200',\n", "    'last_maintenance_margin': '0',\n", "    'long_market_value': '73.07',\n", "    'maintenance_margin': '0',\n", "    'multiplier': '1',\n", "    'non_marginable_buying_power': '126.76',\n", "    'options_approved_level': 3,\n", "    'options_buying_power': '126.76',\n", "    'options_trading_level': 3,\n", "    'pattern_day_trader': <PERSON><PERSON><PERSON>,\n", "    'pending_transfer_in': None,\n", "    'pending_transfer_out': None,\n", "    'portfolio_value': '199.83',\n", "    'regt_buying_power': '126.76',\n", "    'short_market_value': '0',\n", "    'shorting_enabled': <PERSON><PERSON><PERSON>,\n", "    'sma': '0',\n", "    'status': <AccountStatus.ACTIVE: 'ACTIVE'>,\n", "    'trade_suspended_by_user': <PERSON><PERSON><PERSON>,\n", "    'trading_blocked': <PERSON><PERSON><PERSON>,\n", "    'transfers_blocked': False}"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load environment variables from the .env file\n", "load_dotenv()\n", "access_key = os.getenv(\"ALPACA_DEV_ACCESS_KEY_2\")\n", "secret_key = os.getenv(\"ALPACA_DEV_SECRET_KEY_2\")\n", "\n", "from alpaca.trading.client import TradingClient\n", "trading_client = TradingClient(api_key=access_key, secret_key=secret_key, paper=True)\n", "account = trading_client.get_account()\n", "\n", "account"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["clock = trading_client.get_clock()\n", "clock.is_open"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.datetime(2025, 2, 18, 9, 30, tzinfo=TzInfo(-05:00))"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["clock.next_open"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Historical data"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>trade_count</th>\n", "      <th>vwap</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>timestamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"11\" valign=\"top\">GME</th>\n", "      <th>2022-07-01 10:00:00+00:00</th>\n", "      <td>123.020</td>\n", "      <td>123.0200</td>\n", "      <td>123.0200</td>\n", "      <td>123.0200</td>\n", "      <td>211.0</td>\n", "      <td>6.0</td>\n", "      <td>123.020000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 11:00:00+00:00</th>\n", "      <td>122.300</td>\n", "      <td>122.3000</td>\n", "      <td>122.3000</td>\n", "      <td>122.3000</td>\n", "      <td>179.0</td>\n", "      <td>8.0</td>\n", "      <td>122.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 12:00:00+00:00</th>\n", "      <td>122.780</td>\n", "      <td>122.7800</td>\n", "      <td>122.3000</td>\n", "      <td>122.3000</td>\n", "      <td>710.0</td>\n", "      <td>24.0</td>\n", "      <td>122.456863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 13:00:00+00:00</th>\n", "      <td>122.280</td>\n", "      <td>125.1706</td>\n", "      <td>120.7601</td>\n", "      <td>122.6800</td>\n", "      <td>270920.0</td>\n", "      <td>4688.0</td>\n", "      <td>123.143275</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-07-01 14:00:00+00:00</th>\n", "      <td>122.995</td>\n", "      <td>123.9014</td>\n", "      <td>119.2900</td>\n", "      <td>120.7600</td>\n", "      <td>285387.0</td>\n", "      <td>5373.0</td>\n", "      <td>121.091725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-29 20:00:00+00:00</th>\n", "      <td>27.445</td>\n", "      <td>27.6200</td>\n", "      <td>27.2800</td>\n", "      <td>27.5000</td>\n", "      <td>602344.0</td>\n", "      <td>5706.0</td>\n", "      <td>27.488663</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-29 21:00:00+00:00</th>\n", "      <td>27.510</td>\n", "      <td>27.8400</td>\n", "      <td>27.3000</td>\n", "      <td>27.5000</td>\n", "      <td>192883.0</td>\n", "      <td>722.0</td>\n", "      <td>27.506515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-29 22:00:00+00:00</th>\n", "      <td>27.550</td>\n", "      <td>27.5500</td>\n", "      <td>27.3800</td>\n", "      <td>27.4984</td>\n", "      <td>41486.0</td>\n", "      <td>187.0</td>\n", "      <td>27.485134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-29 23:00:00+00:00</th>\n", "      <td>27.500</td>\n", "      <td>27.5000</td>\n", "      <td>27.4300</td>\n", "      <td>27.5000</td>\n", "      <td>21812.0</td>\n", "      <td>88.0</td>\n", "      <td>27.486763</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-30 00:00:00+00:00</th>\n", "      <td>27.530</td>\n", "      <td>27.7400</td>\n", "      <td>27.4708</td>\n", "      <td>27.6800</td>\n", "      <td>43172.0</td>\n", "      <td>252.0</td>\n", "      <td>27.652374</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>9953 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                     open      high       low     close  \\\n", "symbol timestamp                                                          \n", "GME    2022-07-01 10:00:00+00:00  123.020  123.0200  123.0200  123.0200   \n", "       2022-07-01 11:00:00+00:00  122.300  122.3000  122.3000  122.3000   \n", "       2022-07-01 12:00:00+00:00  122.780  122.7800  122.3000  122.3000   \n", "       2022-07-01 13:00:00+00:00  122.280  125.1706  120.7601  122.6800   \n", "       2022-07-01 14:00:00+00:00  122.995  123.9014  119.2900  120.7600   \n", "...                                   ...       ...       ...       ...   \n", "       2025-01-29 20:00:00+00:00   27.445   27.6200   27.2800   27.5000   \n", "       2025-01-29 21:00:00+00:00   27.510   27.8400   27.3000   27.5000   \n", "       2025-01-29 22:00:00+00:00   27.550   27.5500   27.3800   27.4984   \n", "       2025-01-29 23:00:00+00:00   27.500   27.5000   27.4300   27.5000   \n", "       2025-01-30 00:00:00+00:00   27.530   27.7400   27.4708   27.6800   \n", "\n", "                                    volume  trade_count        vwap  \n", "symbol timestamp                                                     \n", "GME    2022-07-01 10:00:00+00:00     211.0          6.0  123.020000  \n", "       2022-07-01 11:00:00+00:00     179.0          8.0  122.300000  \n", "       2022-07-01 12:00:00+00:00     710.0         24.0  122.456863  \n", "       2022-07-01 13:00:00+00:00  270920.0       4688.0  123.143275  \n", "       2022-07-01 14:00:00+00:00  285387.0       5373.0  121.091725  \n", "...                                    ...          ...         ...  \n", "       2025-01-29 20:00:00+00:00  602344.0       5706.0   27.488663  \n", "       2025-01-29 21:00:00+00:00  192883.0        722.0   27.506515  \n", "       2025-01-29 22:00:00+00:00   41486.0        187.0   27.485134  \n", "       2025-01-29 23:00:00+00:00   21812.0         88.0   27.486763  \n", "       2025-01-30 00:00:00+00:00   43172.0        252.0   27.652374  \n", "\n", "[9953 rows x 7 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# keys required\n", "stock_client = StockHistoricalDataClient(access_key, secret_key)\n", "request_params = StockBarsRequest(\n", "                        symbol_or_symbols=[\"GME\"],\n", "                        timeframe=TimeFrame.Hour,\n", "                        start=datetime(2022, 7, 1),\n", "                        end=datetime(2025, 1, 30)\n", "                 )\n", "bars = stock_client.get_stock_bars(request_params)\n", "\n", "# convert to dataframe\n", "bars.df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.00017268208832836834"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["base_risk = 0.01  # 1% base risk\n", "max_risk = 0.02   # 2% maximum risk\n", "stop_loss_pct = 0.02\n", "risk_per_trade = base_risk + (max_risk - base_risk) * 0.8\n", "risk_amount = risk_per_trade * 100\n", "# Calculate stop loss based on ATR\n", "stop_loss = max(stop_loss_pct * 20268.4600, 214.423699)  # Use larger of percentage or 2*ATR\n", "# Calculate base position size\n", "position_size = risk_amount / stop_loss\n", "max_position_pct = 0.01 + 0.05 * 0.5\n", "max_position = max_position_pct * 100 / 20268.4600\n", "max_position\n", "\n", "min(position_size, max_position)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["405.3692"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["stop_loss"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Data cleaning and feature engineering"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>123.020</td>\n", "      <td>123.0200</td>\n", "      <td>123.0200</td>\n", "      <td>123.02</td>\n", "      <td>2022-07-01 10:00:00+00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>122.300</td>\n", "      <td>122.3000</td>\n", "      <td>122.3000</td>\n", "      <td>122.30</td>\n", "      <td>2022-07-01 11:00:00+00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>122.780</td>\n", "      <td>122.7800</td>\n", "      <td>122.3000</td>\n", "      <td>122.30</td>\n", "      <td>2022-07-01 12:00:00+00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>122.280</td>\n", "      <td>125.1706</td>\n", "      <td>120.7601</td>\n", "      <td>122.68</td>\n", "      <td>2022-07-01 13:00:00+00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>122.995</td>\n", "      <td>123.9014</td>\n", "      <td>119.2900</td>\n", "      <td>120.76</td>\n", "      <td>2022-07-01 14:00:00+00:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      open      high       low   close                      date\n", "0  123.020  123.0200  123.0200  123.02 2022-07-01 10:00:00+00:00\n", "1  122.300  122.3000  122.3000  122.30 2022-07-01 11:00:00+00:00\n", "2  122.780  122.7800  122.3000  122.30 2022-07-01 12:00:00+00:00\n", "3  122.280  125.1706  120.7601  122.68 2022-07-01 13:00:00+00:00\n", "4  122.995  123.9014  119.2900  120.76 2022-07-01 14:00:00+00:00"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["history = bars.df\n", "history.reset_index(inplace=True)\n", "history['date'] = pd.to_datetime(history['timestamp'])\n", "history.drop(columns=['symbol'], inplace=True)\n", "history.drop(columns=['timestamp','volume', \"trade_count\", \"vwap\"], inplace=True)\n", "history.head()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_130634/2846232107.py:57: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['atr'].replace(0, 1e-6, inplace=True)\n"]}], "source": ["import ta\n", "#Convert 'date' column to datetime\n", "history['date'] = pd.to_datetime(history['date'])\n", "\n", "# 1. Price change\n", "history['price_change'] = history['close'] - history['open']\n", "\n", "# 2. Price volatility\n", "history['price_volatility'] = history['high'] - history['low']\n", "\n", "# 3. <PERSON><PERSON> returns\n", "history['hourly_return'] = history['close'].pct_change()\n", "\n", "# 4. Moving averages\n", "history['MA_3'] = history['close'].rolling(window=3).mean()\n", "history['MA_24'] = history['close'].rolling(window=24).mean()\n", "\n", "# 5. Price momentum\n", "history['momentum'] = history['close'].diff()\n", "\n", "# 7. Time-based features\n", "history['hour'] = history['date'].dt.hour\n", "history['day_of_week'] = history['date'].dt.dayofweek\n", "\n", "# 8. Price levels (simple example - you might want to use a more sophisticated method)\n", "history['support'] = history['low'].rolling(window=24).min()\n", "history['resistance'] = history['high'].rolling(window=24).max()\n", "\n", "# 9. RSI\n", "def calculate_rsi(series, period=14):\n", "    delta = series.diff()\n", "    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()\n", "    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()\n", "    rs = gain / loss\n", "    return 100 - (100 / (1 + rs))\n", "\n", "history['RSI'] = calculate_rsi(history['close'])\n", "\n", "# 10. Bollinger Bands\n", "history['BB_middle'] = history['close'].rolling(window=20).mean()\n", "history['BB_std'] = history['close'].rolling(window=20).std()\n", "history['BB_upper'] = history['BB_middle'] + (history['BB_std'] * 2)\n", "history['BB_lower'] = history['BB_middle'] - (history['BB_std'] * 2)\n", "\n", "# Drop rows with NaN values resulting from calculations\n", "history.dropna(inplace=True)\n", "# Function to add technical indicators\n", "def add_technical_indicators(df):\n", "    df['macd'] = ta.trend.macd(df['close'])\n", "    df['macd_signal'] = ta.trend.macd_signal(df['close'])\n", "    df['rsi'] = ta.momentum.rsi(df['close'])\n", "    df['bb_high'] = ta.volatility.bollinger_hband(df['close'])\n", "    df['bb_low'] = ta.volatility.bollinger_lband(df['close'])\n", "    df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])\n", "    \n", "    # Avoid division by zero errors (replace 0s in 'atr')\n", "    df['atr'].replace(0, 1e-6, inplace=True)\n", "    return df\n", "\n", "# Prepare historical data (assuming it's already loaded)\n", "history = add_technical_indicators(history)\n", "history['target'] = (history['close'].shift(-1) > history['close']).astype(int)\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>date</th>\n", "      <th>price_change</th>\n", "      <th>price_volatility</th>\n", "      <th>hourly_return</th>\n", "      <th>MA_3</th>\n", "      <th>MA_24</th>\n", "      <th>...</th>\n", "      <th>BB_std</th>\n", "      <th>BB_upper</th>\n", "      <th>BB_lower</th>\n", "      <th>macd</th>\n", "      <th>macd_signal</th>\n", "      <th>rsi</th>\n", "      <th>bb_high</th>\n", "      <th>bb_low</th>\n", "      <th>atr</th>\n", "      <th>target</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9948</th>\n", "      <td>27.445</td>\n", "      <td>27.62</td>\n", "      <td>27.2800</td>\n", "      <td>27.5000</td>\n", "      <td>2025-01-29 20:00:00+00:00</td>\n", "      <td>0.0550</td>\n", "      <td>0.3400</td>\n", "      <td>0.002004</td>\n", "      <td>27.466667</td>\n", "      <td>27.316179</td>\n", "      <td>...</td>\n", "      <td>0.139338</td>\n", "      <td>27.672067</td>\n", "      <td>27.114713</td>\n", "      <td>0.059822</td>\n", "      <td>0.050964</td>\n", "      <td>57.565570</td>\n", "      <td>27.665011</td>\n", "      <td>27.121769</td>\n", "      <td>0.273193</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9949</th>\n", "      <td>27.510</td>\n", "      <td>27.84</td>\n", "      <td>27.3000</td>\n", "      <td>27.5000</td>\n", "      <td>2025-01-29 21:00:00+00:00</td>\n", "      <td>-0.0100</td>\n", "      <td>0.5400</td>\n", "      <td>0.000000</td>\n", "      <td>27.481667</td>\n", "      <td>27.335763</td>\n", "      <td>...</td>\n", "      <td>0.106908</td>\n", "      <td>27.631956</td>\n", "      <td>27.204324</td>\n", "      <td>0.061961</td>\n", "      <td>0.053163</td>\n", "      <td>57.565570</td>\n", "      <td>27.626542</td>\n", "      <td>27.209738</td>\n", "      <td>0.292250</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9950</th>\n", "      <td>27.550</td>\n", "      <td>27.55</td>\n", "      <td>27.3800</td>\n", "      <td>27.4984</td>\n", "      <td>2025-01-29 22:00:00+00:00</td>\n", "      <td>-0.0516</td>\n", "      <td>0.1700</td>\n", "      <td>-0.000058</td>\n", "      <td>27.499467</td>\n", "      <td>27.361113</td>\n", "      <td>...</td>\n", "      <td>0.048717</td>\n", "      <td>27.539994</td>\n", "      <td>27.345126</td>\n", "      <td>0.062804</td>\n", "      <td>0.055091</td>\n", "      <td>57.461077</td>\n", "      <td>27.537527</td>\n", "      <td>27.347593</td>\n", "      <td>0.283518</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9951</th>\n", "      <td>27.500</td>\n", "      <td>27.50</td>\n", "      <td>27.4300</td>\n", "      <td>27.5000</td>\n", "      <td>2025-01-29 23:00:00+00:00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0700</td>\n", "      <td>0.000058</td>\n", "      <td>27.499467</td>\n", "      <td>27.386112</td>\n", "      <td>...</td>\n", "      <td>0.050355</td>\n", "      <td>27.545630</td>\n", "      <td>27.344210</td>\n", "      <td>0.062875</td>\n", "      <td>0.056648</td>\n", "      <td>57.544071</td>\n", "      <td>27.543079</td>\n", "      <td>27.346761</td>\n", "      <td>0.268267</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9952</th>\n", "      <td>27.530</td>\n", "      <td>27.74</td>\n", "      <td>27.4708</td>\n", "      <td>27.6800</td>\n", "      <td>2025-01-30 00:00:00+00:00</td>\n", "      <td>0.1500</td>\n", "      <td>0.2692</td>\n", "      <td>0.006545</td>\n", "      <td>27.559467</td>\n", "      <td>27.418592</td>\n", "      <td>...</td>\n", "      <td>0.072834</td>\n", "      <td>27.601588</td>\n", "      <td>27.310252</td>\n", "      <td>0.076574</td>\n", "      <td>0.060633</td>\n", "      <td>65.660906</td>\n", "      <td>27.597900</td>\n", "      <td>27.313940</td>\n", "      <td>0.268334</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 27 columns</p>\n", "</div>"], "text/plain": ["        open   high      low    close                      date  price_change  \\\n", "9948  27.445  27.62  27.2800  27.5000 2025-01-29 20:00:00+00:00        0.0550   \n", "9949  27.510  27.84  27.3000  27.5000 2025-01-29 21:00:00+00:00       -0.0100   \n", "9950  27.550  27.55  27.3800  27.4984 2025-01-29 22:00:00+00:00       -0.0516   \n", "9951  27.500  27.50  27.4300  27.5000 2025-01-29 23:00:00+00:00        0.0000   \n", "9952  27.530  27.74  27.4708  27.6800 2025-01-30 00:00:00+00:00        0.1500   \n", "\n", "      price_volatility  hourly_return       MA_3      MA_24  ...    BB_std  \\\n", "9948            0.3400       0.002004  27.466667  27.316179  ...  0.139338   \n", "9949            0.5400       0.000000  27.481667  27.335763  ...  0.106908   \n", "9950            0.1700      -0.000058  27.499467  27.361113  ...  0.048717   \n", "9951            0.0700       0.000058  27.499467  27.386112  ...  0.050355   \n", "9952            0.2692       0.006545  27.559467  27.418592  ...  0.072834   \n", "\n", "       BB_upper   BB_lower      macd  macd_signal        rsi    bb_high  \\\n", "9948  27.672067  27.114713  0.059822     0.050964  57.565570  27.665011   \n", "9949  27.631956  27.204324  0.061961     0.053163  57.565570  27.626542   \n", "9950  27.539994  27.345126  0.062804     0.055091  57.461077  27.537527   \n", "9951  27.545630  27.344210  0.062875     0.056648  57.544071  27.543079   \n", "9952  27.601588  27.310252  0.076574     0.060633  65.660906  27.597900   \n", "\n", "         bb_low       atr  target  \n", "9948  27.121769  0.273193       0  \n", "9949  27.209738  0.292250       0  \n", "9950  27.347593  0.283518       1  \n", "9951  27.346761  0.268267       1  \n", "9952  27.313940  0.268334       0  \n", "\n", "[5 rows x 27 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["history.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Train and evalvalueate the model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### more realisticitc backtesting"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py:540: FitFailedWarning: \n", "500 fits failed out of a total of 1000.\n", "The score on these train-test partitions for these parameters will be set to nan.\n", "If these failures are not expected, you can try to debug them by setting error_score='raise'.\n", "\n", "Below are more details about the failures:\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.27256 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.41997 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.26319 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.32444 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.49374 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.21297 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.24149 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.12653 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.17338 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.16211 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.21339 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.32252 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.32433 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.14058 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.26809 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.41654 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.26212 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.00315 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.38111 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.27064 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.44352 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.40469 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.18501 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.31998 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.46451 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.2897 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.4783 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.37509 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.04747 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.07484 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.47738 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.12137 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.49685 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.17581 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.154 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.43462 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.46435 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.39512 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.11181 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.3735 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.42617 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.08425 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.44316 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.02868 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.36916 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.36429 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.34164 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.46523 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.02259 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "--------------------------------------------------------------------------------\n", "10 fits failed with the following error:\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_validation.py\", line 888, in _fit_and_score\n", "    estimator.fit(X_train, y_train, **fit_params)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/sklearn.py\", line 1531, in fit\n", "    self._Booster = train(\n", "                    ^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 726, in inner_f\n", "    return func(**kwargs)\n", "           ^^^^^^^^^^^^^^\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/training.py\", line 181, in train\n", "    bst.update(dtrain, iteration=i, fobj=obj)\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 2100, in update\n", "    _check_call(\n", "  File \"/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/xgboost/core.py\", line 284, in _check_call\n", "    raise XGBoostError(py_str(_LIB.XGBGetLastError()))\n", "xgboost.core.XGBoostError: value 1.35277 for Parameter subsample exceed bound [0,1]\n", "subsample: Row subsample ratio of training instance.\n", "\n", "  warnings.warn(some_fits_failed_message, FitFailedWarning)\n", "/home/<USER>/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/sklearn/model_selection/_search.py:1103: UserWarning: One or more of the test scores are non-finite: [       nan        nan        nan        nan 0.46902494 0.49545379\n", " 0.47313938 0.45256108 0.49738256        nan 0.47458348 0.49638115\n", "        nan 0.49644496 0.47492024 0.50320049        nan 0.46150045\n", " 0.48662446        nan        nan        nan        nan 0.48397778\n", " 0.48523842        nan 0.49300774        nan 0.48729805 0.48510218\n", "        nan        nan 0.47560728 0.50084793        nan        nan\n", "        nan        nan        nan        nan        nan 0.46533403\n", " 0.48162539        nan 0.48273942 0.49669331        nan 0.48008885\n", "        nan        nan        nan 0.47122465 0.4846708         nan\n", " 0.47676534        nan        nan        nan        nan 0.47014016\n", "        nan 0.47396353        nan        nan 0.45774036 0.4943262\n", " 0.49135185 0.50102319 0.47794936 0.44288067        nan        nan\n", " 0.49211237 0.47875745        nan        nan        nan 0.48896323\n", " 0.47631888 0.49527625 0.50012595 0.39346524 0.49239098        nan\n", " 0.48996632        nan 0.49622435 0.49555676        nan        nan\n", "        nan        nan 0.47499841        nan        nan        nan\n", " 0.49731735        nan 0.49122755 0.49048113]\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy: 0.5218\n", "Precision: 0.4969\n", "Recall: 0.5382\n", "F1 Score: 0.5167\n", "Confusion Matrix:\n", "[[2393 2327]\n", " [1972 2298]]\n", "\n", "Top 5 Important Features:\n", "   feature  importance\n", "3    close    0.111841\n", "0     open    0.107492\n", "1     high    0.101527\n", "7  bb_high    0.099453\n", "4     macd    0.098962\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import TimeSeriesSplit\n", "from sklearn.preprocessing import StandardScaler\n", "from xgboost import XGBClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "from sklearn.model_selection import RandomizedSearchCV\n", "from scipy.stats import randint, uniform\n", "import ta\n", "\n", "\n", "# Define features\n", "features = ['open', 'high', 'low', 'close', 'macd', 'macd_signal', 'rsi', 'bb_high', 'bb_low', 'atr']\n", "\n", "# Prepare the data\n", "X = history[features].dropna()\n", "y = history['target'].loc[X.index]\n", "\n", "# Time series split for validation\n", "tscv = TimeSeriesSplit(n_splits=10)\n", "\n", "# Scale the features\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "# Model choice: XGBoost for better performance on financial data\n", "model = XGBClassifier(random_state=42)\n", "\n", "# Hyperparameter tuning using random search\n", "param_dist = {\n", "    'n_estimators': randint(100, 600),\n", "    'max_depth': ran<PERSON>t(3, 15),\n", "    'learning_rate': uniform(0.01, 0.3),\n", "    'subsample': uniform(0.5, 1.0)\n", "}\n", "\n", "random_search = RandomizedSearchCV(model, param_distributions=param_dist, \n", "                                   n_iter=100, cv=tscv, scoring='f1', n_jobs=-1)\n", "random_search.fit(X_scaled, y)\n", "\n", "# Best model after tuning\n", "best_model = random_search.best_estimator_\n", "\n", "import joblib\n", "# Save the best model\n", "joblib.dump(best_model, 'XGBClassifier_v1.pkl')\n", "\n", "# Walk-forward validation\n", "predictions = []\n", "actual = []\n", "for train_index, test_index in tscv.split(X_scaled):\n", "    X_train, X_test = X_scaled[train_index], X_scaled[test_index]\n", "    y_train, y_test = y.iloc[train_index], y.iloc[test_index]\n", "    \n", "    model = XGBClassifier(**random_search.best_params_)\n", "    model.fit(X_train, y_train)\n", "    \n", "    predictions.extend(model.predict(X_test))\n", "    actual.extend(y_test)\n", "\n", "# Performance metrics\n", "accuracy = accuracy_score(actual, predictions)\n", "precision = precision_score(actual, predictions)\n", "recall = recall_score(actual, predictions)\n", "f1 = f1_score(actual, predictions)\n", "conf_matrix = confusion_matrix(actual, predictions)\n", "\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(f\"Precision: {precision:.4f}\")\n", "print(f\"Recall: {recall:.4f}\")\n", "print(f\"F1 Score: {f1:.4f}\")\n", "print(\"Confusion Matrix:\")\n", "print(conf_matrix)\n", "\n", "# Feature importance\n", "feature_importance = pd.DataFrame({'feature': features, 'importance': best_model.feature_importances_})\n", "feature_importance = feature_importance.sort_values('importance', ascending=False)\n", "print(\"\\nTop 5 Important Features:\")\n", "print(feature_importance.head())\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simulator"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/workspace/hedgehog-backend/notebooks/Backtests.py:104: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '67.76459161850273' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  history.loc[history.index[i], 'current_capital'] = capital\n", "/home/<USER>/workspace/hedgehog-backend/notebooks/Backtests.py:105: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '0.2678021214855932' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  history.loc[history.index[i], 'current_position'] = position\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Mean hourly return: 16.051689470747597\n", "Std dev of hourly returns: nan\n", "Number of trades: 1006\n", "Annualization factor: 93.59487165438073\n", "Min return: 16.051689470747597\n", "Max return: 16.051689470747597\n", "Return distribution: \n", "count     1.000000\n", "mean     16.051689\n", "std            NaN\n", "min      16.051689\n", "25%      16.051689\n", "50%      16.051689\n", "75%      16.051689\n", "max      16.051689\n", "dtype: float64\n", "Rolling Sharpe ratio: \n", "count    0.0\n", "mean     NaN\n", "std      NaN\n", "min      <PERSON><PERSON>\n", "25%      NaN\n", "50%      NaN\n", "75%      NaN\n", "max      <PERSON>\n", "dtype: float64\n", "\n", "Trading Simulation Results:\n", "Final Capital: 1243.77\n", "Total Return: 11.44\n", "Annualized Return: 1.66\n", "Win Rate: 0.61\n", "Average Win: 0.05\n", "Average Loss: -0.04\n", "Profit Factor: 2.12\n", "Sharpe Ratio: nan\n", "Sortino Ratio: nan\n", "Max Drawdown: 0.00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/workspace/hedgehog-backend/notebooks/Backtests.py:144: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  hourly_returns = trade_returns.resample('H').sum().fillna(0)\n"]}], "source": ["from Backtests import TradingSimulator\n", "#Usage example:\n", "simulator = TradingSimulator()\n", "# Initialize columns for simulation\n", "history['current_capital'] = 100  # Starting with $100\n", "history['current_position'] = 0\n", "history['position_type'] = 'none'\n", "\n", "metrics, updated_history = simulator.run_simulation(history, best_model, scaler, features)\n", "print(\"\\nTrading Simulation Results:\")\n", "for key, value in metrics.items():\n", "    print(f\"{key}: {value:.2f}\" if isinstance(value, float) else f\"{key}: {value}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["86863.30232850001"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update initial capital to 5000 dollars\n", "initial_capital = 5000  # Initial capital in dollars\n", "\n", "# Calculate final capital after 3 years with the same growth rate\n", "final_capital = initial_capital * (1 + 0.77) ** 5\n", "final_capital\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["Timestamp('2024-09-30 00:00:00+0000', tz='UTC')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["history.index.max()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["Timestamp('2022-07-01 23:00:00+0000', tz='UTC')"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["history.index.min()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Certainly! Let's break down the `calculate_position_size` function and explain it in simple terms:\n", "\n", "\n", "\n", "The `calculate_position_size` function is designed to determine how many shares or units of an asset to buy or sell in a trade. It takes into account several factors to manage risk and adjust the position size based on the model's confidence. Let's go through each part:\n", "\n", "1. Function parameters:\n", "   - `confidence`: How sure the model is about its prediction (a value between 0 and 1)\n", "   - `capital`: The total amount of money available for trading\n", "   - `current_price`: The current price of the asset\n", "   - `atr`: The Average True Range, a measure of market volatility\n", "   - `max_risk_pct`: The maximum percentage of capital you're willing to risk on a single trade (default is 2% or 0.02)\n", "\n", "2. Calculating risk per share:\n", "   ```python\n", "   risk_per_share = atr * 2\n", "   ```\n", "   - The Average True Range (ATR) is used as a measure of the asset's volatility.\n", "   - Multiplying ATR by 2 is a common practice to set a conservative stop-loss level.\n", "   - This means we're assuming that if the price moves against us by twice the ATR, we'll exit the trade.\n", "\n", "3. Calculating the maximum number of shares based on risk:\n", "   ```python\n", "   max_shares = (capital * max_risk_pct) / risk_per_share\n", "   ```\n", "   - We first calculate the maximum amount of money we're willing to risk: `capital * max_risk_pct`\n", "   - Then we divide this by the risk per share to get the maximum number of shares we can buy while staying within our risk tolerance.\n", "\n", "4. Determining the final position size:\n", "   ```python\n", "   return min(max_shares, capital / current_price) * confidence\n", "   ```\n", "   - `capital / current_price` calculates how many shares we could buy if we used all our capital.\n", "   - We take the minimum of this and `max_shares` to ensure we don't exceed our risk tolerance or our available capital.\n", "   - Finally, we multiply by the model's confidence. This means we'll take a larger position when the model is more confident, and a smaller position when it's less confident.\n", "\n", "In simple terms, this function does the following:\n", "1. It figures out how much we could lose per share based on the market's current volatility.\n", "2. It calculates how many shares we can buy while risking no more than 2% of our capital.\n", "3. It makes sure we're not trying to buy more shares than we can afford.\n", "4. It adjusts the position size based on how confident our model is about the trade.\n", "\n", "This approach helps manage risk by:\n", "- Limiting the maximum loss on any single trade to 2% of the capital (adjustable via `max_risk_pct`)\n", "- Taking larger positions in less volatile markets and smaller positions in more volatile markets\n", "- Adjusting position sizes based on the model's confidence, which can help optimize returns\n", "\n", "Remember, this is a simplified approach to position sizing. In real-world trading, you might consider additional factors or use more complex methods, but this provides a good balance of risk management and potential returns for a basic trading simulation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### DEBUG"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initialized keys - Access Key present: PK6T61O3RJ3U8ZPAA7UL, Secret Key present: 6nPrkT3H9Yt0tXX2A8731gvESqjeKogKfjpoYPxl\n", "Equity: 199.85\n"]}, {"ename": "APIError", "evalue": "{\"available\":\"0.000750538\",\"balance\":\"0.000750538\",\"code\":40310000,\"message\":\"insufficient balance for BTC (requested: 0.********, available: 0.000750538)\",\"symbol\":\"USD\"}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHTTPError\u001b[0m                                 Traceback (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/alpaca/common/rest.py:196\u001b[0m, in \u001b[0;36mRESTClient._one_request\u001b[0;34m(self, method, url, opts, retry)\u001b[0m\n\u001b[1;32m    195\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 196\u001b[0m     \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_for_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    197\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m HTTPError \u001b[38;5;28;01mas\u001b[39;00m http_error:\n\u001b[1;32m    198\u001b[0m     \u001b[38;5;66;03m# retry if we hit Rate Limit\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/requests/models.py:1024\u001b[0m, in \u001b[0;36mResponse.raise_for_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1023\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m http_error_msg:\n\u001b[0;32m-> 1024\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HTTPError(http_error_msg, response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m)\n", "\u001b[0;31mHTTPError\u001b[0m: 403 Client Error: Forbidden for url: https://paper-api.alpaca.markets/v2/orders", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31m<PERSON>IE<PERSON>r\u001b[0m                                  Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 25\u001b[0m\n\u001b[1;32m     17\u001b[0m market_order_data \u001b[38;5;241m=\u001b[39m MarketOrderRequest(\n\u001b[1;32m     18\u001b[0m                     symbol\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBTC/USD\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     19\u001b[0m                     qty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.********\u001b[39m,\n\u001b[1;32m     20\u001b[0m                     side\u001b[38;5;241m=\u001b[39mOrderSide\u001b[38;5;241m.\u001b[39mSELL,\n\u001b[1;32m     21\u001b[0m                     time_in_force\u001b[38;5;241m=\u001b[39mTimeInForce\u001b[38;5;241m.\u001b[39mIOC,\n\u001b[1;32m     22\u001b[0m                     )\n\u001b[1;32m     24\u001b[0m \u001b[38;5;66;03m### BULLISH SIGNAL ###\u001b[39;00m\n\u001b[0;32m---> 25\u001b[0m OrderData \u001b[38;5;241m=\u001b[39m \u001b[43mtrading_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msubmit_order\u001b[49m\u001b[43m(\u001b[49m\u001b[43morder_data\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmarket_order_data\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/alpaca/trading/client.py:98\u001b[0m, in \u001b[0;36mTradingClient.submit_order\u001b[0;34m(self, order_data)\u001b[0m\n\u001b[1;32m     89\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Creates an order to buy or sell an asset.\u001b[39;00m\n\u001b[1;32m     90\u001b[0m \n\u001b[1;32m     91\u001b[0m \u001b[38;5;124;03mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     95\u001b[0m \u001b[38;5;124;03m    alpaca.trading.models.Order: The resulting submitted order.\u001b[39;00m\n\u001b[1;32m     96\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     97\u001b[0m data \u001b[38;5;241m=\u001b[39m order_data\u001b[38;5;241m.\u001b[39mto_request_fields()\n\u001b[0;32m---> 98\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/orders\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_use_raw_data:\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/alpaca/common/rest.py:238\u001b[0m, in \u001b[0;36mRESTClient.post\u001b[0;34m(self, path, data)\u001b[0m\n\u001b[1;32m    225\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m    226\u001b[0m     \u001b[38;5;28mself\u001b[39m, path: \u001b[38;5;28mstr\u001b[39m, data: Optional[Union[\u001b[38;5;28mdict\u001b[39m, List[\u001b[38;5;28mdict\u001b[39m]]] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    227\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m HTTPResult:\n\u001b[1;32m    228\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Performs a single POST request\u001b[39;00m\n\u001b[1;32m    229\u001b[0m \n\u001b[1;32m    230\u001b[0m \u001b[38;5;124;03m    Args:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    236\u001b[0m \u001b[38;5;124;03m        dict: The response\u001b[39;00m\n\u001b[1;32m    237\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 238\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mPOST\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/alpaca/common/rest.py:129\u001b[0m, in \u001b[0;36mRESTClient._request\u001b[0;34m(self, method, path, data, base_url, api_version)\u001b[0m\n\u001b[1;32m    127\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m retry \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    128\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 129\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_one_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    130\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m RetryException:\n\u001b[1;32m    131\u001b[0m         time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retry_wait)\n", "File \u001b[0;32m~/miniconda3/envs/hedgehog-backend/lib/python3.12/site-packages/alpaca/common/rest.py:205\u001b[0m, in \u001b[0;36mRESTClient._one_request\u001b[0;34m(self, method, url, opts, retry)\u001b[0m\n\u001b[1;32m    202\u001b[0m     \u001b[38;5;66;03m# raise API error for all other errors\u001b[39;00m\n\u001b[1;32m    203\u001b[0m     error \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mtext\n\u001b[0;32m--> 205\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m APIError(error, http_error)\n\u001b[1;32m    207\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mtext \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    208\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\u001b[38;5;241m.\u001b[39mjson()\n", "\u001b[0;31mAPIError\u001b[0m: {\"available\":\"0.000750538\",\"balance\":\"0.000750538\",\"code\":40310000,\"message\":\"insufficient balance for BTC (requested: 0.********, available: 0.000750538)\",\"symbol\":\"USD\"}"]}], "source": ["from alpaca.trading.requests import MarketOrderRequest\n", "from alpaca.trading.requests  import TakeProfitRequest, StopLossRequest\n", "from alpaca.trading.enums import OrderSide, TimeInForce\n", "from alpaca.trading.client import TradingClient \n", "import os\n", "\n", "  # secret_key = os.getenv(\"ALPACA_DEV_SECRET_KEY\")\n", "access_key = os.getenv(\"ALPACA_DEV_ACCESS_KEY_2\")\n", "secret_key = os.getenv(\"ALPACA_DEV_SECRET_KEY_2\")\n", "print(f\"Initialized keys - Access Key present: {access_key}, Secret Key present: {secret_key}\")\n", "#paper=True enables paper trading\n", "trading_client = TradingClient(access_key, secret_key, paper=True)\n", "account = trading_client.get_account()\n", "\n", "print(f\"Equity: {account.equity}\")\n", "# preparing order\n", "market_order_data = MarketOrderRequest(\n", "                    symbol=\"BTC/USD\",\n", "                    qty=0.********,\n", "                    side=OrderSide.SELL,\n", "                    time_in_force=TimeInForce.IOC,\n", "                    )\n", "\n", "### BULLISH SIGNAL ###\n", "OrderData = trading_client.submit_order(order_data=market_order_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-04 16:43:18.471\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mmessage_handler\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1m{\"id\":\"49781e8a-46dd-4fa3-b5c8-aba0f90b7e3f\",\"status\":200,\"result\":{\"symbol\":\"BTCUSDT\",\"priceChange\":\"-780.********\",\"priceChangePercent\":\"-0.810\",\"weightedAvgPrice\":\"95804.********\",\"openPrice\":\"96352.********\",\"highPrice\":\"96509.********\",\"lowPrice\":\"95209.********\",\"lastPrice\":\"95571.********\",\"volume\":\"9367.30081000\",\"quoteVolume\":\"897427352.89377590\",\"openTime\":1746283380000,\"closeTime\":1746369797465,\"firstId\":4871691794,\"lastId\":4873133171,\"count\":1441378},\"rateLimits\":[{\"rateLimitType\":\"REQUEST_WEIGHT\",\"interval\":\"MINUTE\",\"intervalNum\":1,\"limit\":6000,\"count\":10}]}\u001b[0m\n", "\u001b[32m2025-05-04 16:43:23.255\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mclosing ws connection\u001b[0m\n", "CLOSE frame received, closing websocket connection\n"]}], "source": ["from loguru import logger\n", "import time\n", "# WebSocket API Client\n", "from binance.websocket.spot.websocket_api import SpotWebsocketAPIClient\n", "\n", "def message_handler(_, message):\n", "    logger.info(message)\n", "\n", "my_client = SpotWebsocketAPIClient(on_message=message_handler)\n", "\n", "my_client.ticker(symbol=\"BTCUSDT\", type=\"FULL\")\n", "\n", "time.sleep(5)\n", "logger.info(\"closing ws connection\")\n", "my_client.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "hedgehog-backend", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}