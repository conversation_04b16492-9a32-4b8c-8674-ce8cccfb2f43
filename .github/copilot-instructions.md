# Hedgehog Trading System AI Agent Instructions

This document provides guidance for AI agents working on the Hedgehog Trading System codebase.

## 1. Architecture Overview

The system is a FastAPI application designed for automated cryptocurrency trading. The architecture is modular and event-driven.

- **Entrypoint**: `app/main.py` initializes the FastAPI app and uses a `lifespan` context manager to start a background streaming service (`app.services.streaming.start_streaming`). This service is critical as it fetches live market data.
- **Core Logic**: `app/services/hedgehog.py` contains the `Hedgehog` class, which orchestrates the trading process. It follows a Strategy design pattern, composed of:
    - **Strategy**: (`app/services/models/xgboost_strategy.py`) Generates trading signals. It uses a pre-trained XGBoost model (`XGBClassifier_v1.pkl`) and creates technical analysis features (RSI, MACD, etc.) from market data.
    - **Risk Manager**: (`app/services/risk_managers.py`) Calculates position sizing based on model confidence, capital, and market volatility (ATR).
    - **Broker**: (`app/services/brokers.py`) An interface (`BrokerInterface`) for interacting with trading exchanges. Concrete implementations like `AlpacaBroker` and `BinanceBroker` handle order execution and account management.
- **API**: `app/api/routes.py` defines the HTTP endpoints. The primary endpoint likely triggers the `Hedgehog.engage_market()` method to execute a trade based on the latest data.
- **Data Flow**:
    1. The streaming service in `app/services/streaming.py` fetches live data.
    2. This data is passed to `Hedgehog.engage_market()`.
    3. The `xgboost_strategy` generates a buy/sell prediction.
    4. The `naive_risk_model` determines the trade size.
    5. The `Hedgehog` class uses a `Broker` implementation to place the order on the exchange.

## 2. Developer Workflows

### Running the Application

The application is containerized using Docker.

- **To build and run with Docker Compose**:
  ```bash
  docker-compose up --build
  ```
- **To run locally for development**:
  1. Install dependencies: `pip install -r requirements.txt`
  2. Run the FastAPI server: `python -m uvicorn app.main:app --reload --loop asyncio`

The application will be available at `http://localhost:2222` (or as configured). The API documentation is at `http://localhost:2222/docs`.

### Environment Variables

The application uses a `.env` file for configuration (e.g., API keys for brokers). Ensure you have a `.env` file in the root directory with the necessary credentials. Example:

```
ALPACA_API_KEY="YOUR_KEY"
ALPACA_SECRET_KEY="YOUR_SECRET"
# ... other keys
```

## 3. Code Conventions & Patterns

- **Broker Abstraction**: All interactions with trading exchanges are done through the `BrokerInterface` in `app/services/brokers.py`. When adding support for a new exchange, create a new class that implements this interface.
- **Strategy Pattern**: Trading logic is encapsulated in strategy classes like `xgboost_strategy`. To test a new model or feature engineering approach, create a new class that follows the existing structure.
- **Configuration**: The application uses `app/config.py` to load settings and API keys from environment variables. Add new configurations there.
- **Logging**: The `loguru` library is used for structured logging. Logs are written to `runtime.log`. Use the `logger` object for all logging.
- **Asynchronous Operations**: The application uses `asyncio` and `nest_asyncio`. The streaming service runs in a background task. Be mindful of the async nature of the application, especially when dealing with I/O.
