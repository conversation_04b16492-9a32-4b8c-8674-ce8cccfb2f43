import typer
import requests
import time
from rich.console import Console
from rich.table import Table

app = typer.Typer()
console = Console()

BACKEND_URL = "http://127.0.0.1:2222/api/v1"

@app.command()
def run(
    strategy: str = typer.Option("xgboost_v1", help="Strategy to run."),
    symbol: str = typer.Option("BTCUSDT", help="Trading symbol."),
    start_date: str = typer.Option("2023-01-01", help="Start date (YYYY-MM-DD)."),
    end_date: str = typer.Option("2023-02-01", help="End date (YYYY-MM-DD)."),
    initial_capital: float = typer.Option(10000.0, help="Initial capital."),
    timeframe: str = typer.Option("1h", help="Timeframe (e.g., '1h', '1d').")
):
    """
    Run a new backtest.
    """
    console.print(f"🚀 Starting backtest for [bold]{symbol}[/bold] from {start_date} to {end_date}...")
    
    try:
        response = requests.post(f"{BACKEND_URL}/backtests/run", json={
            "strategy": strategy,
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date,
            "initial_capital": initial_capital,
            "timeframe": timeframe
        })
        response.raise_for_status()
        
        run_id = response.json()["run_id"]
        console.print(f"✅ Backtest initiated with [bold]Run ID: {run_id}[/bold]")

        # Poll for results
        with console.status("[bold green]Backtest running...") as status:
            while True:
                status_response = requests.get(f"{BACKEND_URL}/backtests/{run_id}/status")
                status_response.raise_for_status()
                current_status = status_response.json()["status"]
                
                if current_status == "completed":
                    console.print("✅ Backtest completed!")
                    break
                elif current_status.startswith("failed"):
                    console.print(f"❌ Backtest failed: {current_status}")
                    return
                
                time.sleep(5)
        
        # Fetch and display results
        results_response = requests.get(f"{BACKEND_URL}/backtests/{run_id}/results")
        results_response.raise_for_status()
        results = results_response.json()

        display_results(results)

    except requests.exceptions.RequestException as e:
        console.print(f"❌ Error communicating with the backend: {e}")

def display_results(results: dict):
    """Displays backtest results in a formatted table."""
    
    console.print("\n--- Backtest Results ---")
    
    # Summary Table
    summary_table = Table(title="Backtest Summary")
    summary_table.add_column("Parameter", style="cyan")
    summary_table.add_column("Value", style="magenta")
    summary_table.add_row("Run ID", results.get('run_id'))
    summary_table.add_row("Symbol", results.get('symbol'))
    summary_table.add_row("Period", f"{results.get('start_date')} to {results.get('end_date')}")
    summary_table.add_row("Initial Capital", f"${results.get('initial_capital'):,.2f}")
    summary_table.add_row("Final Portfolio Value", f"${results.get('final_portfolio_value'):,.2f}")
    console.print(summary_table)

    # Performance Metrics Table
    metrics = results.get("performance_metrics", {})
    perf_table = Table(title="Performance Metrics")
    perf_table.add_column("Metric", style="cyan")
    perf_table.add_column("Value", style="magenta")
    for key, value in metrics.items():
        display_value = f"{value:.4f}" if isinstance(value, float) else str(value)
        perf_table.add_row(key.replace("_", " ").title(), display_value)
    console.print(perf_table)

    # Optional: Display trade history if not too long
    trades = results.get("trade_history", [])
    if trades:
        console.print(f"\n📈 Found {len(trades)} trades. Displaying the first 10:")
        trades_table = Table(title="Trade History (First 10)")
        trades_table.add_column("Timestamp")
        trades_table.add_column("Type")
        trades_table.add_column("Price")
        trades_table.add_column("Size")
        trades_table.add_column("PnL")

        for trade in trades[:10]:
            trades_table.add_row(
                trade['timestamp'],
                trade['type'],
                f"{trade['price']:.2f}",
                f"{trade['size']:.4f}",
                f"{trade['pnl']:.2f}"
            )
        console.print(trades_table)


if __name__ == "__main__":
    app()
