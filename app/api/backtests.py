from fastapi import APIRouter, BackgroundTasks, HTTPException
from pydantic import BaseModel
import uuid
from loguru import logger

from app.services.backtesting.engine import run_backtest
from app.services.backtesting.data import fetch_historical_data
from app.services.backtesting.models import BacktestResult

router = APIRouter()

# In-memory storage for backtest status and results
backtest_status = {}
backtest_results = {}

class BacktestRunRequest(BaseModel):
    strategy: str
    symbol: str
    start_date: str
    end_date: str
    initial_capital: float
    timeframe: str

def run_backtest_in_background(run_id: str, request: BacktestRunRequest):
    """
    A wrapper function to run the backtest in a background task.
    """
    try:
        backtest_status[run_id] = "running"
        
        # 1. Fetch data
        data_df = fetch_historical_data(
            symbol=request.symbol,
            interval=request.timeframe,
            start_str=request.start_date,
            end_str=request.end_date
        )
        
        if data_df.empty:
            raise ValueError("No historical data fetched.")

        # 2. Run backtest
        result = run_backtest(
            strategy_name=request.strategy,
            data_df=data_df,
            initial_capital=request.initial_capital,
            symbol=request.symbol,
            timeframe=request.timeframe,
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        # 3. Store results and update status
        backtest_results[run_id] = result
        backtest_status[run_id] = "completed"

    except Exception as e:
        logger.exception(f"Backtest run {run_id} failed.")
        backtest_status[run_id] = f"failed: {repr(e)}"

@router.post("/backtests/run", status_code=202)
async def run_new_backtest(request: BacktestRunRequest, background_tasks: BackgroundTasks):
    """
    Triggers a new backtest run in the background.

    Args:
        request: The request body containing backtest parameters.
        background_tasks: FastAPI's background task runner.

    Returns:
        A dictionary containing the unique ID for the backtest run.
    """
    run_id = str(uuid.uuid4())
    background_tasks.add_task(run_backtest_in_background, run_id, request)
    return {"run_id": run_id}

@router.get("/backtests/{run_id}/status")
async def get_backtest_status(run_id: str):
    """
    Retrieves the status of a specific backtest run.

    Args:
        run_id: The unique ID of the backtest run.

    Returns:
        A dictionary containing the current status of the run.
    """
    status = backtest_status.get(run_id)
    if not status:
        raise HTTPException(status_code=404, detail="Backtest run not found")
    return {"status": status}

@router.get("/backtests/{run_id}/results", response_model=BacktestResult)
async def get_backtest_results(run_id: str):
    """
    Retrieves the results of a completed backtest run.

    Args:
        run_id: The unique ID of the backtest run.

    Returns:
        The full BacktestResult object.
    """
    result = backtest_results.get(run_id)
    if not result:
        raise HTTPException(status_code=404, detail="Backtest results not found")
    
    status = backtest_status.get(run_id)
    if status != "completed":
        raise HTTPException(status_code=400, detail=f"Backtest is not completed. Current status: {status}")
        
    return result

@router.get("/backtests/runs")
async def get_all_backtest_runs():
    """
    Returns a list of all backtest runs and their statuses.
    """
    return backtest_status
