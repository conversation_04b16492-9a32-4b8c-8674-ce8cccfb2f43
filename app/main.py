from fastapi import <PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager
import asyncio
from loguru import logger
from .services.streaming import start_streaming
from .api.routes import router
from .api.backtests import router as backtests_router
from fastapi.middleware.wsgi import WSGIMiddleware
from .dashboard.app import dash_app
import nest_asyncio

nest_asyncio.apply()
# Configure structured logging
logger.add(
    "runtime.log",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    rotation="1 MB",
    retention="1 days",
    backtrace=True,
    diagnose=True
)

@asynccontextmanager
async def lifespan(app: FastAPI) -> None:
    """
    Manages application lifecycle events with proper concurrent streaming.
    """
    logger.info("Application startup initiated. Launching streaming service...")
    
    # Create the streaming task but don't await it
    streaming_task = asyncio.create_task(start_streaming())
    
    try:
        yield  # Let FastAPI run
    finally:
        logger.info("Application shutdown initiated. Releasing resources...")
        streaming_task.cancel()
        try:
            # Wait for the streaming task to properly clean up
            await asyncio.wait_for(streaming_task, timeout=5.0)
        except asyncio.TimeoutError:
            logger.warning("Streaming service shutdown timed out")
        except asyncio.CancelledError:
            logger.info("Streaming service terminated gracefully")
        except Exception as e:
            logger.error(f"Error during streaming service shutdown: {e}")

app = FastAPI(
    title="Hedgehog Trading System",
    description="Automated crypto trading system with risk management",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# Include all routes
app.include_router(router, prefix="/api/v1", tags=["trading"])
app.include_router(backtests_router, prefix="/api/v1", tags=["backtesting"])

# Mount the Dash app
app.mount("/dashboard", WSGIMiddleware(dash_app.server))

# Root endpoint for basic health check
@app.get("/", tags=["health"])
async def read_root():
    return {
        "status": "healthy",
        "message": "Welcome to the Hedgehog Trading System",
        "version": "1.0.0",
        "docs_url": "/docs",
        "api_prefix": "/api/v1"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0", 
        port=2222,
        log_level="info",
        loop="asyncio",
        workers=1  # Multiple workers not recommended with the streaming setup
    )