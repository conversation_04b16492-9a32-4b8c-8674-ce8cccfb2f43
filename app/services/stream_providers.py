from abc import ABC, abstractmethod
from typing import Callable, Any

class StreamProviderInterface(ABC):
    @abstractmethod
    def start(self):
        pass

    @abstractmethod
    def stop(self):
        pass

    @abstractmethod
    def subscribe_ticker(self, symbol: str, callback: Callable[[Any], None]):
        pass

    @abstractmethod
    def subscribe_user(self, callback: Callable[[Any], None]):
        pass

# --- Alpaca Stream Provider ---
class AlpacaStreamProvider(StreamProviderInterface):
    def __init__(self, api_key: str, api_secret: str):
        from alpaca.data.live.stock import StockDataStream
        self.stream = StockDataStream(api_key=api_key, secret_key=api_secret)
        self._running = False

    def start(self):
        self._running = True
        # Alpaca's StockDataStream is async, so you must await run() in the event loop
        # This method is a placeholder for compatibility

    def stop(self):
        self._running = False
        # You should call await self.stream.stop() in the event loop

    def subscribe_ticker(self, symbol: str, callback: Callable[[Any], None]):
        # For Alpaca, subscribe to trades or quotes
        self.stream.subscribe_trades(callback, symbol)

    def subscribe_user(self, callback: Callable[[Any], None]):
        # Alpaca does not support user/account websocket in the same way as Binance
        pass

# --- Binance Stream Provider ---
class BinanceStreamProvider(StreamProviderInterface):
    def __init__(self, api_key: str, api_secret: str):
        from binance.streams import ThreadedWebsocketManager
        self.twm = ThreadedWebsocketManager(api_key=api_key, api_secret=api_secret)
        self._running = False

    def start(self):
        self.twm.start()
        self._running = True

    def stop(self):
        self.twm.stop()
        self._running = False

    def subscribe_ticker(self, symbol: str, callback: Callable[[Any], None]):
        self.twm.start_symbol_ticker_socket(callback=callback, symbol=symbol)

    def subscribe_user(self, callback: Callable[[Any], None]):
        self.twm.start_user_socket(callback=callback)
