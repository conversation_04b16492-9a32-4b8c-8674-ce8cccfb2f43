
=== STATE-OF-THE-ART BTC PREDICTION MODEL SUMMARY ===

Best Model: logistic_regression
F1 Score: 0.4902
Cross-Validation F1: 0.5747 (+/- 0.0838)

Features Used: 67
Training Samples: 14356
Test Samples: 2871

Model Components:
- Advanced feature engineering with 69 initial features
- Multi-method feature selection
- Robust scaling for outlier resistance
- Ensemble modeling with 5 base models

Trading Performance:
Best Confidence Threshold: 0.6

Total Return: -0.3946
Sharpe Ratio: -2.4840
Max Drawdown: -0.3946
Number of Trades: 150
Win Rate: 0.1267


Files Saved:
- Model: /home/<USER>/workspace/hedgehog-backend/app/services/models/advanced/logistic_regression_v3.pkl
- Scaler: /home/<USER>/workspace/hedgehog-backend/app/services/models/advanced/RobustScaler_v3.pkl
- Features: /home/<USER>/workspace/hedgehog-backend/app/services/models/advanced/feature_info_v3.pkl
- Metrics: /home/<USER>/workspace/hedgehog-backend/app/services/models/advanced/model_metrics_v3.pkl
- Preprocessing: /home/<USER>/workspace/hedgehog-backend/app/services/models/advanced/preprocessing_components_v3.pkl

=== END SUMMARY ===
