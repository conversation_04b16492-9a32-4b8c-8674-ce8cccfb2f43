
from loguru import logger
import pandas as pd 
import re
 
class naive_risk_model:

    def __init__(self, stop_loss_pct=0.02, take_profit_pct = 0.03):
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        self.current_price = 0

        self.monitored_positions = {}

    def calculate_position_size(self, confidence: float, capital: float, price: float, atr: float) -> float:

        can_trade = False
        self.current_price = price
        if capital >= 10:
            try:
                # Convert inputs to float to ensure type consistency
                confidence = float(confidence)
                capital = float(capital)
                price = float(price)
                atr = float(atr)
                
                logger.info(f"Calculating position size with confidence: {confidence}")
                
                # Validate inputs
                if any(pd.isna([confidence, capital, price, atr])):
                    raise ValueError("Input contains NaN values")
                
                base_risk = 0.01  # 1% base risk
                max_risk = 0.02   # 2% maximum risk
                risk_per_trade = base_risk + (max_risk - base_risk) * confidence
                risk_amount = float(risk_per_trade * capital)
                logger.info(f"Risk Amount: {risk_amount}")
                
                # Calculate stop loss based on ATR
                stop_loss = float(max(self.stop_loss_pct * price, 2 * atr))
                logger.info(f"Stop Loss: {stop_loss}")
                
                # Calculate position size with explicit float conversion
                position_size = float(risk_amount / stop_loss)
                logger.info(f"Base Position Size: {position_size}")
                
                # Calculate maximum position size
                max_position_pct = 0.3 + 0.2 * confidence
                max_position = float(max_position_pct * capital / price)
                logger.info(f"Max Position: {max_position}")
                
                # Return the smaller of the two values
                final_position = min(position_size, max_position)
                
                # Round to 8 decimal places (common for crypto)
                final_position = round(final_position, 8)
                
                logger.info(f"Final Position Size: {final_position}")
                can_trade = True if final_position > 0 else False
                return final_position, can_trade
                
            except Exception as e:
                logger.error(f"Error calculating position size: {e}")
                return 0, can_trade
        else:
            logger.info("Capital is less than $10, cannot trade")
            return 0, can_trade
        

    ####TODO This needs further logical implemetation to make it work for moving stop loss and take profit ####
        
    def monitor_positions(self, trading_client, stop_loss_price, take_profit_price, asset_id):

        self.monitored_positions[asset_id] = {
            "stop_loss": stop_loss_price,
            "take_profit": take_profit_price,
            "asset_id": asset_id
        }
        
        logger.info(f"Monitoring new position {asset_id} with Stop Loss: {stop_loss_price}, Take Profit: {take_profit_price}")

        #### Manual stop loss and take profit monitoring ####
        for position in trading_client.get_all_positions():
            stop_loss = self.monitored_positions[position.asset_id]["stop_loss"]
            take_profit = self.monitored_positions[position.asset_id]["take_profit"]

            if self.current_price <= stop_loss:
                logger.info(f"Stop Loss hit for position {position.asset_id}. Closing position...")
                logger.info(f"Change today {position.change_today},\nunrealized PnL:  {self.postion.unrealized_pl}\nasset class: {self.postion.asset_class}")
                trading_client.close_position(position.asset_id)
                self.monitored_positions.pop(position.asset_id)
            elif self.current_price >= take_profit:
                logger.info(f"Take Profit hit for position {position.asset_id}. Closing position...")
                logger.info(f"Change today {position.change_today},\nunrealized PnL:  {self.postion.unrealized_pl}\nasset class: {self.postion.asset_class}")
                trading_client.close_position(position.asset_id)
                self.monitored_positions.pop(position.asset_id)

            





