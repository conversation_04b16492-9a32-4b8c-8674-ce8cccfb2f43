from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

# --- Broker Interface ---
class BrokerInterface(ABC):
    @abstractmethod
    def place_market_order(self, symbol: str, side: str, quantity: float, **kwargs) -> Any:
        pass

    @abstractmethod
    def place_limit_order(self, symbol: str, side: str, quantity: float, price: float, **kwargs) -> Any:
        pass

    @abstractmethod
    def place_take_profit(self, symbol: str, side: str, quantity: float, price: float, **kwargs) -> Any:
        pass

    @abstractmethod
    def place_stop_loss(self, symbol: str, side: str, quantity: float, stop_price: float, price: float, **kwargs) -> Any:
        pass

    @abstractmethod
    def get_account_info(self) -> Dict:
        pass

    @abstractmethod
    def get_historical_data(self, symbol: str, interval: str, limit: int = 100) -> Any:
        pass

    @abstractmethod
    def get_realtime_price(self, symbol: str) -> float:
        pass

# --- Alpaca Broker Implementation ---
class AlpacaBroker(BrokerInterface):
    def __init__(self, api_key: str, api_secret: str, paper: bool = True):
        from alpaca.trading.client import TradingClient
        self.trading_client = TradingClient(api_key, api_secret, paper=paper)
        self.account = self.trading_client.get_account()

    def place_market_order(self, symbol, side, quantity, **kwargs):
        from alpaca.trading.requests import MarketOrderRequest
        from alpaca.trading.enums import OrderSide, TimeInForce
        order = MarketOrderRequest(
            symbol=symbol,
            qty=quantity,
            side=OrderSide.BUY if side.upper() == 'BUY' else OrderSide.SELL,
            time_in_force=TimeInForce.DAY
        )
        return self.trading_client.submit_order(order_data=order)

    def place_limit_order(self, symbol, side, quantity, price, **kwargs):
        from alpaca.trading.requests import LimitOrderRequest
        from alpaca.trading.enums import OrderSide, TimeInForce
        order = LimitOrderRequest(
            symbol=symbol,
            qty=quantity,
            side=OrderSide.BUY if side.upper() == 'BUY' else OrderSide.SELL,
            time_in_force=TimeInForce.DAY,
            limit_price=price
        )
        return self.trading_client.submit_order(order_data=order)

    def place_take_profit(self, symbol, side, quantity, price, **kwargs):
        # Alpaca supports bracket orders for take profit/stop loss
        from alpaca.trading.requests import MarketOrderRequest
        from alpaca.trading.enums import OrderSide, TimeInForce, OrderClass
        order = MarketOrderRequest(
            symbol=symbol,
            qty=quantity,
            side=OrderSide.BUY if side.upper() == 'BUY' else OrderSide.SELL,
            time_in_force=TimeInForce.DAY,
            order_class=OrderClass.BRACKET,
            take_profit={'limit_price': price}
        )
        return self.trading_client.submit_order(order_data=order)

    def place_stop_loss(self, symbol, side, quantity, stop_price, price, **kwargs):
        from alpaca.trading.requests import MarketOrderRequest
        from alpaca.trading.enums import OrderSide, TimeInForce, OrderClass
        order = MarketOrderRequest(
            symbol=symbol,
            qty=quantity,
            side=OrderSide.BUY if side.upper() == 'BUY' else OrderSide.SELL,
            time_in_force=TimeInForce.DAY,
            order_class=OrderClass.BRACKET,
            stop_loss={'stop_price': stop_price, 'limit_price': price}
        )
        return self.trading_client.submit_order(order_data=order)

    def get_account_info(self):
        return self.trading_client.get_account().__dict__

    def get_historical_data(self, symbol, interval, limit=100):
        # Placeholder: Implement using Alpaca data API
        return None

    def get_realtime_price(self, symbol):
        # Placeholder: Implement using Alpaca data API
        return None

# --- Binance Broker Implementation ---
class BinanceBroker(BrokerInterface):
    def __init__(self, api_key: str, api_secret: str):
        # Initialize Binance client
        print("Initializing Binance client...")
        from binance.spot import Spot
        self.client = Spot(api_key=api_key, api_secret=api_secret)

    def place_market_order(self, symbol, side, quantity, **kwargs):
        return self.client.new_order(
            symbol=symbol,
            side=side.upper(),
            type='MARKET',
            quantity=quantity
        )

    def place_limit_order(self, symbol, side, quantity, price, **kwargs):
        return self.client.new_order(
            symbol=symbol,
            side=side.upper(),
            type='LIMIT',
            timeInForce='GTC',
            quantity=quantity,
            price=str(price)
        )

    def place_take_profit(self, symbol, side, quantity, price, **kwargs):
        # Take profit is a limit sell
        return self.client.new_order(
            symbol=symbol,
            side=side.upper(),
            type='LIMIT',
            timeInForce='GTC',
            quantity=quantity,
            price=str(price)
        )

    def place_stop_loss(self, symbol, side, quantity, stop_price, price, **kwargs):
        # Stop loss is a stop-limit order
        return self.client.new_order(
            symbol=symbol,
            side=side.upper(),
            type='STOP_LOSS_LIMIT',
            timeInForce='GTC',
            quantity=quantity,
            stopPrice=str(stop_price),
            price=str(price)
        )

    def get_account_info(self):
        return self.client.account()

    def get_historical_data(self, symbol, interval, limit=100):
        return self.client.klines(symbol, interval, limit=limit)

    def get_realtime_price(self, symbol):
        ticker = self.client.ticker_price(symbol)
        return float(ticker['price']) if 'price' in ticker else None
