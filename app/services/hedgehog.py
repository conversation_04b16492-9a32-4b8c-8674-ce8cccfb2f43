from typing import List, Tuple, Literal
from sklearn.preprocessing import StandardScaler
from dotenv import load_dotenv
from loguru import logger
import nest_asyncio
import os
import re
from .brokers import BrokerInterface


class Hedgehog:

    def __init__(self, strategy=None, risk_manager=None, provider: BrokerInterface = None):
        self.strategy = strategy
        self.risk_manager = risk_manager
        self.position = 0
        self.scaler = StandardScaler()
        self.broker = provider
        logger.info("Initialized Hedgehog Trading System with provider: {}".format(type(self.broker).__name__))
        nest_asyncio.apply()

    def engage_market(self, history: float):
        history = self.strategy.generate_features(history)
        X = self.scaler.fit_transform(history.iloc[[-1]][self.strategy.features].dropna())
        logger.info("Obtained features")
        logger.info(f"\nX: {X}")
        prediction = self.strategy.model.predict(X)[0]
        confidence = self.strategy.model.predict_proba(X)[0][1]

        account_info = self.broker.get_account_info()
        capital = float(account_info.get('cash', account_info.get('totalWalletBalance', 0)))
        current_price = history.iloc[-1]['close']
        atr = history.iloc[-1]['atr']

        logger.info(f"Prediction: {prediction}, Confidence: {confidence}, Capital: {capital}, Current Price: {current_price}, ATR: {atr}")
        position_size, can_trade = self.risk_manager.calculate_position_size(float(confidence), float(capital), float(current_price), float(atr))
        logger.info(f"Position Size: {position_size}")

        stop_loss_price = 0
        take_profit_price = 0
        new_asset_id = 0

        symbol = history.iloc[-1].get('symbol', 'BTCUSDT')  # Default to BTCUSDT if not present
        side = 'BUY' if prediction == 1 else 'SELL'

        if can_trade:
            if prediction == 1:  # Buy signal
                stop_loss_price = current_price * (1 - self.risk_manager.stop_loss_pct)
                take_profit_price = current_price * (1 + self.risk_manager.take_profit_pct)
                logger.info(f"Stop Loss Price: {stop_loss_price}, Take Profit Price: {take_profit_price}")
                logger.info(f"Current Price: {current_price}")
                order = self.broker.place_market_order(symbol, side, position_size)
                new_asset_id = order.get('orderId', getattr(order, 'asset_id', 0))
                logger.info(f"Order Data: {order}")
                # Place take profit and stop loss orders if supported
                self.broker.place_take_profit(symbol, 'SELL', position_size, take_profit_price)
                self.broker.place_stop_loss(symbol, 'SELL', position_size, stop_loss_price, stop_loss_price)
            elif prediction == 0:  # Sell signal (Short)
                stop_loss_price = current_price * (1 + self.risk_manager.stop_loss_pct)
                take_profit_price = current_price * (1 - self.risk_manager.take_profit_pct)
                logger.info(f"Stop Loss Price: {stop_loss_price}, Take Profit Price: {take_profit_price}")
                logger.info(f"Current Price: {current_price}")
                order = self.broker.place_market_order(symbol, side, position_size)
                new_asset_id = order.get('orderId', getattr(order, 'asset_id', 0))
                logger.info(f"Order Data: {order}")
                self.broker.place_take_profit(symbol, 'BUY', position_size, take_profit_price)
                self.broker.place_stop_loss(symbol, 'BUY', position_size, stop_loss_price, stop_loss_price)
            self.risk_manager.monitor_positions(self.broker, stop_loss_price, take_profit_price, new_asset_id)
        else:
            logger.info("Cannot trade due to insufficient capital or position size")
            # TODO: Add notification for insufficient capital or position size
            pass


