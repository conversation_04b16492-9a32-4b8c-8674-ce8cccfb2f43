import os
import asyncio
import pandas as pd
import traceback
import time
from datetime import datetime, timedelta
from loguru import logger
from dotenv import load_dotenv
from .hedgehog import Hedgehog
from .risk_managers import naive_risk_model
from .models.xgboost_strategy import xgboost_strategy
from app.services.brokers import BinanceBroker
from binance import ThreadedWebsocketManager
from binance.enums import *
import json

class stockStreamManager:
    def __init__(self):
        load_dotenv(override=False)

        self.hourly_data = {}
        self.last_saved_time = {}
        self.stream_running = False
        self.ohlc_storage = {}

        # Network error configuration
        self.max_reconnect_attempts = 10
        self.base_reconnect_delay = 1  # Start with 1 second
        self.max_reconnect_delay = 120  # Max 2 minutes between attempts

        # Initialize Hedgehog instance with Binance broker
        self.hedgehog_instance = Hedgehog(
            strategy=xgboost_strategy(),
            risk_manager=naive_risk_model(),
            provider=BinanceBroker(api_key=os.getenv('BINANCE_API_KEY'), api_secret=os.getenv('BINANCE_API_SECRET'))
        )

        self.is_connected = False
        self.last_heartbeat = None
        self.client = None
        logger.info("stockStreamManager instance successfully initialized")

    async def initialize_stream(self):
        """Initialize a new WebSocket API client for Binance"""
        try:
            from binance.websocket.spot.websocket_api import SpotWebsocketAPIClient

            # Define message handler
            def message_handler(message):
                logger.info(f"Received message: {message}")
                self._process_ticker_data(message)

            # Create client instance
            self.client = ThreadedWebsocketManager(api_key=os.getenv('BINANCE_API_KEY'), api_secret=os.getenv('BINANCE_API_SECRET'))
            self.client.start()
            self.client.start_kline_socket(symbol="BTCUSDT",callback=message_handler, interval=KLINE_INTERVAL_15MINUTE)
            

            logger.info("Binance WebSocket API client initialized successfully")
            self.is_connected = True
            return self.client

        except Exception as e:
            logger.error(f"Error during WebSocket client initialization: {e}")
            logger.error(traceback.format_exc()) 
            raise

    def _process_ticker_data(self, data):
        """Process ticker data received from WebSocket"""
        try:
            ### convert data to dict if it's a string
            if isinstance(data, str):
                data = json.loads(data)
                logger.info(f"Converted string data to dict: {data}")
            # Check if data contains required fields
            if isinstance(data, dict):
                ticker_data = data
                logger.info(f"Received ticker data: {ticker_data}")
                symbol = ticker_data.get('symbol', 'BTCUSDT')
                price = float(ticker_data.get('l', 0))
                timestamp = datetime.fromtimestamp(ticker_data.get('t', datetime.now().timestamp()) / 1000)

                # Call OHLC aggregation method
                self._aggregate_ohlc(symbol, price, timestamp)
            else:
                logger.warning(f"Received unexpected data format: {data}")

        except Exception as e:
            logger.error(f"Error processing ticker data: {e}")
            logger.error(traceback.format_exc())

    def _aggregate_ohlc(self, symbol, price, timestamp):
        # Simplified OHLC aggregation for streaming
        if symbol not in self.hourly_data:
            self.hourly_data[symbol] = []
            self.last_saved_time[symbol] = timestamp
            logger.info(f"Initialized data storage for symbol: {symbol}")
        self.hourly_data[symbol].append({'timestamp': timestamp, 'price': price})
        if timestamp >= self.last_saved_time[symbol] + timedelta(hours=1):
            df = pd.DataFrame(self.hourly_data[symbol])
            ohlc_data = {
                'open': df['price'].iloc[0],
                'high': df['price'].max(),
                'low': df['price'].min(),
                'close': df['price'].iloc[-1],
                'symbol': symbol,
                'timestamp': timestamp
            }
            if symbol not in self.ohlc_storage:
                self.ohlc_storage[symbol] = []
            self.ohlc_storage[symbol].append(ohlc_data)
            logger.info(f"OHLC data recorded for {symbol} at {timestamp}")
            self.hourly_data[symbol] = []
            self.last_saved_time[symbol] = timestamp
            asyncio.create_task(self._process_historical_data(symbol))
        if symbol in self.ohlc_storage:
            logger.info(f"Total OHLC records for {symbol}: {len(self.ohlc_storage[symbol])}")

    async def _process_historical_data(self, symbol):
        """Process historical data with error handling"""
        try:
            historical_df = pd.DataFrame(self.ohlc_storage[symbol])
            if len(historical_df) >= 24:
                logger.info(f"Historical data retrieved with shape: {historical_df.shape}")

                self.hedgehog_instance.engage_market(historical_df)
            else:
                logger.info("Insufficient historical data for market engagement")
        except Exception as e:
            logger.error(f"Error processing historical data: {e}")

    async def run_stream_forever(self):
        """Run the WebSocket stream with reconnection logic"""
        reconnect_attempts = 0

        try:
            while True:
                try:
                    # Check for cancellation
                    await asyncio.sleep(0)

                    logger.info("Initializing Binance WebSocket streaming service...")
                    await self.initialize_stream()
                    logger.info(f"WebSocket stream started, attempt {reconnect_attempts + 1}")

                    self.stream_running = True
                    logger.info("Stream launched successfully")

                    # Keep the event loop alive and check connection periodically
                    while True:
                        await asyncio.sleep(5)
                        # Add heartbeat check if needed

                except asyncio.CancelledError:
                    logger.info("Stream received cancellation request")
                    raise

                except Exception as e:
                    logger.error(f"Critical: Unexpected stream error encountered: {e}")
                    logger.error("!!! Stream terminated unexpectedly !!!")

                    # Implement exponential backoff for reconnection
                    reconnect_delay = min(
                        self.max_reconnect_delay,
                        self.base_reconnect_delay * (2 ** reconnect_attempts)
                    )
                    logger.info(f"Attempting to reconnect in {reconnect_delay} seconds...")
                    await asyncio.sleep(reconnect_delay)
                    reconnect_attempts += 1

                    if reconnect_attempts >= self.max_reconnect_attempts:
                        logger.error(f"Maximum reconnection attempts ({self.max_reconnect_attempts}) reached. Giving up.")
                        break

                    continue

                finally:
                    # Cleanup resources
                    if self.client is not None:
                        try:
                            self.client.stop()
                            logger.info("WebSocket client stopped. Resources released.")
                            # Reset attempt counter if connected successfully and cleanup was successful
                            reconnect_attempts = 0
                        except Exception as cleanup_error:
                            logger.error(f"Error during WebSocket cleanup: {cleanup_error}")
                        finally:
                            self.client = None
                            self.is_connected = False

        finally:
            # Ensure cleanup happens
            if self.client is not None:
                try:
                    self.client.stop()
                    logger.info("WebSocket client stopped during shutdown")
                except Exception as cleanup_error:
                    logger.error(f"Error during WebSocket cleanup: {cleanup_error}")

async def start_streaming():
    """Start the streaming service with improved concurrency handling."""
    logger.info("Initiating Binance WebSocket streaming service...")

    try:
        manager = stockStreamManager()
        # Run the stream manager with cooperative cancellation
        while True:
            try:
                await manager.run_stream_forever()
            except asyncio.CancelledError:
                logger.info("Streaming service received shutdown signal")
                raise
            except Exception as e:
                logger.error(f"Streaming service error, restarting: {e}")
                await asyncio.sleep(5)  # Brief delay before restart
    except asyncio.CancelledError:
        logger.info("Streaming service shutdown complete")
        raise
    except Exception as e:
        logger.error(f"Fatal error in streaming service: {e}")
        logger.error(traceback.format_exc())
        raise