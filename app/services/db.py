from pymongo import MongoClient
from datetime import datetime, timedelta
from loguru import logger
import pandas as pd
import os

access_key = os.getenv("ALPACA_DEV_ACCESS_KEY")
secret_key = os.getenv("ALPACA_DEV_SECRET_KEY")
# Database connection string CONNECTION_STRING_MONGODB
CONNECTION_STRING = os.getenv("CONNECTION_STRING_MONGODB")
client = MongoClient(CONNECTION_STRING)
db = client['hedgehog_streamed_data']
collection = db['crypto_quotes']

# Add quote to the database
async def add_quote_to_db(quote_data):
    collection.insert_one(quote_data)

# Function to retrieve records from the last specified hours and return DataFrame
def get_records_by_hours(retrieve_hours=24):
    # Calculate the start time for filtering
    current_time = datetime.timezone.utc
    start_time = current_time - timedelta(hours=retrieve_hours)
    
    # Convert start_time to milliseconds since Unix epoch (to match the MongoDB timestamp format)
    start_time_ms = int(start_time.timestamp() * 1000)
    
    # Query the collection for records where timestamp is greater than or equal to start_time_ms
    records = collection.find(
        {"timestamp.$date": {"$gte": start_time_ms}},
        {'_id': 0}
    )
    
    # Convert to list of dictionaries
    records_list = list(records)
    
    # Convert timestamps from milliseconds to datetime
    for record in records_list:
        record['timestamp'] = datetime.utcfromtimestamp(record['timestamp']['$date'] / 1000)
    
    # Load the records into a Pandas DataFrame
    df = pd.DataFrame(records_list)
    logger.info(f"recieved last 2 rows:\n{df.tail(2)}")
    
    return df

# Remove data older than 5 days
async def remove_old_data():
    five_days_ago = datetime.now() - timedelta(days=5)
    collection.delete_many({"timestamp": {"$lt": five_days_ago}})
