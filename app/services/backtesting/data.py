from binance.client import Client
import pandas as pd
from loguru import logger
import os
from datetime import datetime

class BinanceDataFetcher:
    """
    A class to fetch historical data from Binance API.

    This class provides a convenient interface for fetching historical market data
    from Binance, with proper error handling and data formatting.
    """

    def __init__(self):
        """Initialize the BinanceDataFetcher with API credentials from environment variables."""
        self.api_key = os.environ.get('BINANCE_API_KEY')
        self.api_secret = os.environ.get('BINANCE_SECRET_KEY')
        self.client = None

    def _get_client(self):
        """Get or create a Binance client instance."""
        if self.client is None:
            self.client = Client(self.api_key, self.api_secret)
        return self.client

    def fetch(self, symbol: str, timeframe: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """
        Fetch historical data for a given symbol and timeframe.

        Args:
            symbol (str): The trading symbol (e.g., 'BTC/USDT')
            timeframe (str): The timeframe (e.g., '1h', '1d')
            start_date (datetime): Start date for the data
            end_date (datetime): End date for the data

        Returns:
            pd.DataFrame: DataFrame with historical market data
        """
        # Convert symbol format from 'BTC/USDT' to 'BTCUSDT' for Binance API
        binance_symbol = symbol.replace('/', '')

        # Convert datetime objects to string format expected by Binance API
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')

        return fetch_historical_data(binance_symbol, timeframe, start_str, end_str)

def fetch_historical_data(symbol: str, interval: str, start_str: str, end_str: str) -> pd.DataFrame:
    """
    Fetches historical k-line (candlestick) data from the Binance API.

    This function retrieves market data for a specified cryptocurrency symbol, time interval,
    and date range. It handles API authentication, data fetching, and formats the
    data into a pandas DataFrame suitable for financial analysis.

    Args:
        symbol (str): The trading symbol to fetch data for (e.g., 'BTCUSDT').
        interval (str): The k-line interval (e.g., '1h', '1d', '1w').
        start_str (str): The start date for the data in 'YYYY-MM-DD' format.
        end_str (str): The end date for the data in 'YYYY-MM-DD' format.

    Returns:
        pd.DataFrame: A DataFrame containing the historical market data, with columns
                      for timestamp, open, high, low, close, and volume. Returns an
                      empty DataFrame if an error occurs or no data is found.
    """
    try:
        api_key = os.environ.get('BINANCE_API_KEY')
        api_secret = os.environ.get('BINANCE_SECRET_KEY')
        client = Client(api_key, api_secret)

        logger.info(f"Fetching historical data for {symbol} from {start_str} to {end_str} with interval {interval}")
        
        klines = client.get_historical_klines(symbol, interval, start_str, end_str)
        
        if not klines:
            logger.warning("No data returned from Binance.")
            return pd.DataFrame()

        # Define column names for the DataFrame
        columns = [
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ]
        
        df = pd.DataFrame(klines, columns=columns)

        # Convert timestamp to datetime and set as index
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Convert relevant columns to numeric types
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col])

        logger.info(f"Successfully fetched {len(df)} data points.")
        
        return df

    except Exception as e:
        logger.error(f"An error occurred while fetching historical data: {e}")
        return pd.DataFrame()
