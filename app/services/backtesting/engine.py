import backtrader as bt
from loguru import logger
import pandas as pd
import uuid
from datetime import datetime

from app.services.backtesting.strategies import XGBoostStrategyAdapter
from app.services.backtesting.models import BacktestResult, Trade

def run_backtest(strategy_name: str, data_df: pd.DataFrame, initial_capital: float, symbol: str, timeframe: str, start_date: str, end_date: str) -> BacktestResult:
    """
    Configures and runs a backtest using the Backtrader engine.

    This function orchestrates the entire backtesting process. It initializes the
    Backtrader engine (`Cerebro`), adds the historical data feed, sets the trading
    strategy, defines initial capital, attaches performance analyzers, and runs
    the simulation. Finally, it extracts, formats, and returns the results.

    Args:
        strategy_name (str): The name of the strategy to use (e.g., 'xgboost_v1').
        data_df (pd.DataFrame): A DataFrame containing the historical market data.
        initial_capital (float): The starting capital for the backtest.
        symbol (str): The trading symbol (e.g., 'BTCUSDT').
        timeframe (str): The time interval of the data (e.g., '1h').
        start_date (str): The start date of the backtest period.
        end_date (str): The end date of the backtest period.

    Returns:
        BacktestResult: A Pydantic model object containing the detailed results
                        of the backtest, including performance metrics, trade
                        history, and a plot.
    """
    cerebro = bt.Cerebro()

    # Add data feed
    # The data feed needs a datetime index
    data_df['timestamp'] = pd.to_datetime(data_df['timestamp'])
    data_df.set_index('timestamp', inplace=True)
    data = bt.feeds.PandasData(dataname=data_df)
    cerebro.adddata(data)

    # Add strategy
    if strategy_name.startswith('xgboost'):
        cerebro.addstrategy(XGBoostStrategyAdapter)
    else:
        raise ValueError(f"Strategy '{strategy_name}' not recognized.")

    # Set initial capital
    cerebro.broker.setcash(initial_capital)

    # Add analyzers for performance metrics
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='tradeanalyzer')
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')

    logger.info("Starting backtest...")
    results = cerebro.run()
    logger.info("Backtest finished.")

    # Extract results
    strat = results[0]
    analyzers = strat.analyzers

    # Performance metrics
    trade_analysis = analyzers.tradeanalyzer.get_analysis()
    
    total_trades = trade_analysis.get('total', {}).get('total', 0)
    won_trades = trade_analysis.get('won', {}).get('total', 0)
    lost_trades = trade_analysis.get('lost', {}).get('total', 0)

    performance_metrics = {
        "net_pnl": analyzers.returns.get_analysis().get('rtot', 0),
        "sharpe_ratio": analyzers.sharpe.get_analysis().get('sharperatio'),
        "max_drawdown": analyzers.drawdown.get_analysis().get('max', {}).get('drawdown', 0),
        "win_loss_ratio": won_trades / lost_trades if lost_trades > 0 else float('inf') if won_trades > 0 else 0,
        "total_trades": total_trades,
        "avg_trade_pnl": trade_analysis.get('pnl', {}).get('net', {}).get('average', 0)
    }

    # Trade history
    trade_history = []
    trades = trade_analysis.get('trades', [])
    if trades:
        for t in trades:
            trade_history.append(Trade(
                type="BUY" if t.size > 0 else "SELL",
                price=t.price,
                size=t.size,
                timestamp=t.open_datetime().isoformat(),
                pnl=t.pnl,
                value=t.value
            ))

    # Plotting
    plot_html = "<div>No plot generated.</div>"
    try:
        # cerebro.plot returns a list of lists of figures
        fig = cerebro.plot(style='plotly', iplot=False, savefig=False)[0][0]
        # Use plotly's io to convert the figure to html
        import plotly.io as pio
        plot_html = pio.to_html(fig, full_html=False, include_plotlyjs='cdn')
    except Exception as e:
        logger.warning(f"Could not generate plot: {e}")

    # Create result object
    backtest_result = BacktestResult(
        run_id=str(uuid.uuid4()),
        strategy=strategy_name,
        symbol=symbol,
        timeframe=timeframe,
        start_date=start_date,
        end_date=end_date,
        initial_capital=initial_capital,
        final_portfolio_value=cerebro.broker.getvalue(),
        performance_metrics=performance_metrics,
        trade_history=trade_history,
        plot_html=plot_html
    )

    return backtest_result
