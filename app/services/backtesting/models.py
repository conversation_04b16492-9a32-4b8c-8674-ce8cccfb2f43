from pydantic import BaseModel
from typing import List, Dict, Any

class Trade(BaseModel):
    type: str
    price: float
    size: float
    timestamp: str
    pnl: float
    value: float

class BacktestResult(BaseModel):
    run_id: str
    strategy: str
    symbol: str
    timeframe: str
    start_date: str
    end_date: str
    initial_capital: float
    final_portfolio_value: float
    performance_metrics: Dict[str, Any]
    trade_history: List[Trade]
    plot_html: str
