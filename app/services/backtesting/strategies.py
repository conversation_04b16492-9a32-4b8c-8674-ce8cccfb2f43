import backtrader as bt
import pandas as pd
from loguru import logger
from sklearn.preprocessing import StandardScaler

# Assuming the original strategy and risk manager are in these locations
from app.services.models.xgboost_strategy import xgboost_strategy as OriginalStrategy
from app.services.risk_managers import naive_risk_model as RiskManager

class XGBoostStrategyAdapter(bt.Strategy):
    """
    A Backtrader strategy that wraps the existing XGBoost-based trading strategy.
    """
    def __init__(self):
        self.original_strategy = OriginalStrategy()
        self.risk_manager = RiskManager()
        self.scaler = StandardScaler()
        self.order = None

    def next(self):
        # Convert the backtrader data feeds to a pandas DataFrame
        # that our original strategy can understand.
        # We need to look back a certain number of bars to have enough data for feature generation.
        # Let's assume a lookback of 100 bars is sufficient.
        if len(self.data) < 100:
            return

        # Create a DataFrame from the last 100 bars
        history_data = {
            'timestamp': [bt.num2date(self.data.datetime[-i]) for i in range(100)],
            'open': [self.data.open[-i] for i in range(100)],
            'high': [self.data.high[-i] for i in range(100)],
            'low': [self.data.low[-i] for i in range(100)],
            'close': [self.data.close[-i] for i in range(100)],
            'volume': [self.data.volume[-i] for i in range(100)],
            'symbol': ['BTCUSDT'] * 100 # Assuming a fixed symbol for now
        }
        history_df = pd.DataFrame(history_data)
        history_df = history_df.sort_values(by='timestamp').reset_index(drop=True)

        try:
            # 1. Generate features
            features_df = self.original_strategy.generate_features(history_df)
            
            # We only need the most recent feature set for prediction
            latest_features = features_df.iloc[[-1]][self.original_strategy.features].dropna()
            if latest_features.empty:
                return

            # The original strategy should handle scaling if it was used in training.
            # The adapter should not re-scale the data.
            X = latest_features

            # 2. Get prediction and confidence
            prediction = self.original_strategy.model.predict(X)[0]
            confidence = self.original_strategy.model.predict_proba(X)[0][1]

            # 3. Risk Management
            capital = self.broker.get_cash()
            current_price = self.data.close[0]
            atr = features_df.iloc[-1]['atr']

            position_size, can_trade = self.risk_manager.calculate_position_size(
                confidence, capital, current_price, atr
            )

            # 4. Execute trade
            if self.position: # If we are in a position
                if (prediction == 0 and self.position.size > 0): # and signal is to sell
                    logger.info("Signal to SELL, closing existing BUY position.")
                    self.close()
            elif can_trade:
                if prediction == 1: # Buy signal
                    logger.info(f"Signal to BUY, placing order for size {position_size}")
                    self.buy(size=position_size)
                # Note: Short selling is not implemented here for simplicity,
                # as it requires margin account settings.

        except Exception as e:
            logger.error(f"Error in strategy's next method: {e}")

    def log(self, txt, dt=None):
        ''' Logging function for this strategy'''
        dt = dt or self.datas[0].datetime.date(0)
        logger.info(f'{dt.isoformat()}, {txt}')

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            # Buy/Sell order submitted/accepted to/by broker - Nothing to do
            return

        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'BUY EXECUTED, Price: {order.executed.price:.2f}, Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}')
            elif order.issell():
                self.log(f'SELL EXECUTED, Price: {order.executed.price:.2f}, Cost: {order.executed.value:.2f}, Comm: {order.executed.comm:.2f}')
            self.bar_executed = len(self)

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')

        self.order = None
