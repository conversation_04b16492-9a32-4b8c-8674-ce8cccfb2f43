import dash
from dash import dcc, html
from dash.dependencies import Input, Output
import plotly.graph_objects as go
import requests
import pandas as pd

# Assuming the FastAPI backend is running at this address
BACKEND_URL = "http://127.0.0.1:2222"

# Initialize the Dash app
dash_app = dash.Dash(__name__, suppress_callback_exceptions=True, requests_pathname_prefix='/dashboard/')

dash_app.layout = html.Div([
    html.H1("Hedgehog Backtest Dashboard"),
    
    # Interval component to periodically fetch available backtest runs
    dcc.Interval(
        id='interval-component',
        interval=10*1000, # in milliseconds (every 10 seconds)
        n_intervals=0
    ),
    
    # Dropdown to select a backtest run
    dcc.Dropdown(
        id='backtest-run-dropdown',
        placeholder="Select a Backtest Run...",
    ),
    
    html.Hr(),
    
    # Div to display the results (plot and stats)
    html.Div(id='backtest-results-display')
])

from dash.dependencies import Input, Output, State
from app.dashboard.views import generate_results_layout

# This callback will be implemented in T017
@dash_app.callback(
    Output('backtest-run-dropdown', 'options'),
    [Input('interval-component', 'n_intervals')]
)
def update_dropdown_options(n):
    """Periodically fetches the list of all backtest runs to populate the dropdown."""
    try:
        response = requests.get(f"{BACKEND_URL}/api/v1/backtests/runs")
        response.raise_for_status()
        runs = response.json()
        options = [{'label': f"{run_id} ({status})", 'value': run_id} for run_id, status in runs.items()]
        return options
    except requests.exceptions.RequestException as e:
        print(f"Error fetching backtest runs: {e}")
        return []

@dash_app.callback(
    Output('backtest-results-display', 'children'),
    [Input('backtest-run-dropdown', 'value')]
)
def display_backtest_results(run_id):
    """Fetches and displays the results for the selected backtest run."""
    if not run_id:
        return html.Div("Please select a backtest run to view results.")

    try:
        # First, check the status
        status_response = requests.get(f"{BACKEND_URL}/api/v1/backtests/{run_id}/status")
        status_response.raise_for_status()
        status = status_response.json().get('status')

        if status != 'completed':
            return html.Div(f"Backtest status: {status}. Please wait for completion.")

        # If completed, fetch the full results
        results_response = requests.get(f"{BACKEND_URL}/api/v1/backtests/{run_id}/results")
        results_response.raise_for_status()
        results_data = results_response.json()
        
        return generate_results_layout(results_data)

    except requests.exceptions.RequestException as e:
        return html.Div(f"An error occurred while fetching results: {e}")


if __name__ == '__main__':
    dash_app.run_server(debug=True, port=8051)
