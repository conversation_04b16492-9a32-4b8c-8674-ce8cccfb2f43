from dash import html, dcc
import pandas as pd

def create_stats_table(title: str, metrics: dict) -> html.Table:
    """Creates a simple HTML table for displaying performance metrics."""
    header = [html.Thead(html.Tr([html.Th(title, colSpan="2")]))]
    body = [html.Tbody([
        html.Tr([html.Td(key.replace("_", " ").title()), html.Td(f"{value:.2f}" if isinstance(value, float) else value)])
        for key, value in metrics.items()
    ])]
    return html.Table(header + body, className="stats-table")

def create_trades_table(trades: list) -> html.Table:
    """Creates an HTML table to display the history of trades."""
    if not trades:
        return html.P("No trades were executed.")
        
    df = pd.DataFrame(trades)
    header = [html.Thead(html.Tr([html.Th(col.title()) for col in df.columns]))]
    body = [html.Tbody([
        html.Tr([
            html.Td(df.iloc[i][col]) for col in df.columns
        ]) for i in range(len(df))
    ])]
    return html.Table(header + body, className="trades-table")

def generate_results_layout(results_data: dict) -> html.Div:
    """
    Generates the Dash component layout for displaying backtest results.
    
    Args:
        results_data: A dictionary containing the backtest results from the API.
        
    Returns:
        A Dash html.Div component containing the formatted results.
    """
    if not results_data:
        return html.Div()

    # Main plot from backtrader
    plot_html = results_data.get("plot_html", "<div>Plot could not be generated.</div>")
    
    # Performance Stats
    stats = results_data.get("performance_metrics", {})
    summary_stats = {
        "Final Portfolio Value": results_data.get("final_portfolio_value"),
        "Net PnL": stats.get("net_pnl"),
        "Sharpe Ratio": stats.get("sharpe_ratio"),
        "Max Drawdown": stats.get("max_drawdown"),
        "Total Trades": stats.get("total_trades"),
        "Win/Loss Ratio": stats.get("win_loss_ratio"),
    }

    # Trade History
    trades = results_data.get("trade_history", [])

    layout = html.Div([
        html.H3("Backtest Summary"),
        create_stats_table("Performance Metrics", {k: v for k, v in summary_stats.items() if v is not None}),
        
        html.Hr(),
        
        html.H3("Equity Curve & Trades"),
        # Use an Iframe to safely render the HTML plot from the backend
        html.Iframe(srcDoc=plot_html, style={"width": "100%", "height": "500px", "border": "none"}),
        
        html.Hr(),
        
        html.H3("Trade History"),
        create_trades_table(trades)
    ])
    
    return layout
