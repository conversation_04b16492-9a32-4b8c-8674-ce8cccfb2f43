# Tasks: Backtesting Functionality

**Input**: Design documents from `/specs/001-add-a-back/`
**Prerequisites**: plan.md, research.md, data-model.md, contracts/

## Phase 3.1: Setup
- [ ] T001: Update `requirements.txt` with `backtrader`, `plotly-dash`, `python-binance`.
- [ ] T002: Create the directory structure outlined in `plan.md`:
    - `app/services/backtesting/`
    - `app/api/`
    - `app/dashboard/`
    - `tests/integration/`
    - `tests/unit/`
- [ ] T003: Create empty `__init__.py` files in the new directories to ensure they are recognized as Python packages.

## Phase 3.2: Tests First (TDD)
- [ ] T004 [P]: Create failing contract test for `POST /backtests/run` in `tests/contract/test_backtests_api.py`.
- [ ] T005 [P]: Create failing contract test for `GET /backtests/{run_id}/status` in `tests/contract/test_backtests_api.py`.
- [ ] T006 [P]: Create failing contract test for `GET /backtests/{run_id}/results` in `tests/contract/test_backtests_api.py`.
- [ ] T007 [P]: Create failing unit test for data fetching in `tests/unit/test_data_fetching.py`. This test should check if historical data is correctly retrieved from `python-binance`.
- [ ] T008 [P]: Create failing integration test in `tests/integration/test_backtesting_flow.py` that covers the full backtesting process as described in the user story.

## Phase 3.3: Core Implementation
- [ ] T009: Implement the data fetching logic in `app/services/backtesting/data.py` to get historical data from `python-binance`. Make `T007` pass.
- [ ] T010: Implement the `BacktestResult` data model in `app/services/backtesting/models.py` based on `data-model.md`.
- [ ] T011: Create the `xgboost` strategy adapter in `app/services/backtesting/strategies.py`. This will be a `backtrader` strategy class that wraps the existing `xgboost_strategy`.
- [ ] T012: Implement the backtesting engine in `app/services/backtesting/engine.py`. This module will configure and run the `backtrader` engine with the provided strategy and data.
- [ ] T013: Implement the backtesting API endpoints in `app/api/backtests.py`. This will include the endpoints for running a backtest, checking status, and getting results. Make `T004`, `T005`, `T006` pass.
- [ ] T014: Implement the core logic to make the integration test `T008` pass. This involves wiring together the data fetching, strategy, and engine.

## Phase 3.4: Dashboard
- [ ] T015: Create the basic Dash application layout in `app/dashboard/app.py`.
- [ ] T016: Implement the plotting functions in `app/dashboard/views.py` to create visualizations for the backtest results (e.g., equity curve, trade history).
- [ ] T017: Implement the callbacks in `app/dashboard/app.py` to connect the dropdown for selecting a backtest run to the plotting functions.

## Phase 3.5: Polish
- [ ] T018 [P]: Add comprehensive docstrings to all new functions and classes.
- [ ] T019 [P]: Implement a CLI script `app/cli/backtest.py` as described in `quickstart.md` to run backtests from the command line.
- [ ] T020: Review and refine logging to ensure all critical steps in the backtesting process are logged.

## Dependencies
- `T001`, `T002`, `T003` must be done first.
- Tests (`T004`-`T008`) must be created before their corresponding implementation tasks.
- `T009` (data fetching) is a dependency for `T012` (engine).
- `T011` (strategy adapter) is a dependency for `T012` (engine).
- `T012` (engine) is a dependency for `T013` (API).
- `T014` (integration) depends on all core implementation tasks.
- Dashboard tasks (`T015`-`T017`) can be done after the API is functional (`T013`).

## Parallel Example
The following test creation tasks can be run in parallel:
```
Task: "T004 [P]: Create failing contract test for POST /backtests/run in tests/contract/test_backtests_api.py"
Task: "T005 [P]: Create failing contract test for GET /backtests/{run_id}/status in tests/contract/test_backtests_api.py"
Task: "T006 [P]: Create failing contract test for GET /backtests/{run_id}/results in tests/contract/test_backtests_api.py"
Task: "T007 [P]: Create failing unit test for data fetching in tests/unit/test_data_fetching.py"
Task: "T008 [P]: Create failing integration test in tests/integration/test_backtesting_flow.py"
```
