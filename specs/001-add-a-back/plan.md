# Implementation Plan: Backtesting Functionality


**Branch**: `001-add-a-back` | **Date**: 2025-09-13 | **Spec**: [spec.md](spec.md)
**Input**: Feature specification from `/specs/001-add-a-back/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend+backend, mobile=app+api)
   → Set Structure Decision based on project type
3. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
4. Execute Phase 0 → research.md
   → If NEEDS CLARIFICATION remain: ERROR "Resolve unknowns"
5. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent-specific template file (e.g., `CLAUDE.md` for Claude <PERSON>, `.github/copilot-instructions.md` for GitHub Copilot, or `GEMINI.md` for Gemini CLI).
6. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
   → Update Progress Tracking: Post-Design Constitution Check
7. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
8. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Phases 2-4 are executed by other commands:
- Phase 2: /tasks command creates tasks.md
- Phase 3-4: Implementation execution (manual or via tools)

## Summary
This plan outlines the implementation of a backtesting functionality for the Hedgehog trading system. The core of this feature will be built using the `backtrader` library to simulate trading strategies against historical data. Historical data will be sourced using `python-binance`. The results, including performance metrics and trade history, will be visualized through a web-based dashboard created with `Plotly Dash`. The existing `xgboost_strategy` will be adapted to be compatible with the `backtrader` engine.

## Technical Context
**Language/Version**: Python 3.10+
**Primary Dependencies**: `backtrader`, `plotly-dash`, `python-binance`, `pandas`, `fastapi`, `xgboost`, `scikit-learn`, `ta`
**Storage**: File-based storage for backtest results (e.g., CSV or JSON for metrics and trade logs).
**Testing**: `pytest`
**Target Platform**: Linux server
**Project Type**: Single project (backend service with a web-based UI for results)
**Performance Goals**: [NEEDS CLARIFICATION: What is the expected run time for a 1-year backtest on 1h data?]
**Constraints**: The implementation should be modular to allow for different strategies and data sources in the future.
**Scale/Scope**: The initial scope is to backtest the existing XGBoost strategy on BTC/USDT data.

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

**Simplicity**:
- Projects: 1 (The backtesting module will be integrated into the existing project)
- Using framework directly? Yes, `backtrader` and `dash` will be used as intended.
- Single data model? Yes, for the backtest results.
- Avoiding patterns? Yes, no unnecessary design patterns will be introduced.

**Architecture**:
- EVERY feature as library? The backtesting functionality will be developed as a distinct module within the `app/services` directory, promoting separation of concerns.
- Libraries listed: `backtesting` (to encapsulate backtesting logic).
- CLI per library: A CLI interface will be provided to run backtests.
- Library docs: Docstrings will be used.

**Testing (NON-NEGOTIABLE)**:
- RED-GREEN-Refactor cycle enforced? Yes, tests will be written before implementation.
- Git commits show tests before implementation? Yes.
- Order: Unit tests will be created for individual components. Integration tests will cover the full backtest flow.
- Real dependencies used? Yes, file system for I/O.
- Integration tests for: The full backtest execution from data fetching to result generation.
- FORBIDDEN: Implementation before test, skipping RED phase.

**Observability**:
- Structured logging included? Yes, `loguru` will be used for logging within the backtesting module.
- Frontend logs → backend? N/A for the Dash app in this context.
- Error context sufficient? Yes, logs will capture detailed error information.

**Versioning**:
- Version number assigned? N/A for this feature.
- BUILD increments on every change? N/A.
- Breaking changes handled? N/A.

## Project Structure

### Documentation (this feature)
```
specs/001-add-a-back/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
# Option 1: Single project (DEFAULT)
app/
├── services/
│   ├── backtesting/
│   │   ├── __init__.py
│   │   ├── engine.py       # Backtrader integration
│   │   ├── strategies.py   # Adapters for existing strategies
│   │   └── data.py         # Data fetching (python-binance)
│   └── ...
├── api/
│   └── backtests.py        # API endpoints for backtesting
└── dashboard/
    ├── __init__.py
    ├── app.py              # Dash application
    └── views.py            # Plotly graphs

tests/
├── integration/
│   └── test_backtesting_flow.py
└── unit/
    ├── test_backtesting_engine.py
    └── test_data_fetching.py

```

**Structure Decision**: The project will follow the modified single project structure outlined above to logically group the new backtesting and dashboard components.

## Phase 0: Outline & Research
1. **Extract unknowns from Technical Context** above:
   - Research best practices for integrating a custom ML-based strategy (`xgboost_strategy`) with `backtrader`.
   - Determine the standard set of performance metrics to be calculated and displayed (resolves FR-005).
   - Define the supported timeframes for backtesting (resolves FR-007).
   - Investigate how to best structure a `Plotly Dash` application for displaying backtest results.
   - Clarify performance goals for backtest execution time.

2. **Generate and dispatch research agents**:
   ```
   For each unknown in Technical Context:
     Task: "Research {unknown} for {feature context}"
   For each technology choice:
     Task: "Find best practices for {tech} in {domain}"
   ```

3. **Consolidate findings** in `research.md` using format:
   - Decision: [what was chosen]
   - Rationale: [why chosen]
   - Alternatives considered: [what else evaluated]

**Output**: research.md with all NEEDS CLARIFICATION resolved

## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

1. **Extract entities from feature spec** → `data-model.md`:
   - Define the structure for `BacktestResult`, including the specific performance metrics and the format for trade history.

2. **Generate API contracts** from functional requirements:
   - Define FastAPI endpoints in `/contracts/` for:
     - Starting a new backtest.
     - Checking the status of a running backtest.
     - Retrieving the results of a completed backtest.

3. **Generate contract tests** from contracts:
   - Create `pytest` tests for the backtesting API endpoints. These tests will initially fail.

4. **Extract test scenarios** from user stories:
   - Create an integration test that simulates the full user story: triggering a backtest and fetching the results.
   - The `quickstart.md` will document how to run a backtest via the CLI or API.

5. **Update agent file incrementally** (O(1) operation):
   - Run `/scripts/bash/update-agent-context.sh copilot` for your AI assistant
   - If exists: Add only NEW tech from current plan
   - Preserve manual additions between markers
   - Update recent changes (keep last 3)
   - Keep under 150 lines for token efficiency
   - Output to repository root

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, agent-specific file

## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- Load `/templates/tasks-template.md` as base
- Generate tasks from Phase 1 design docs (contracts, data model, quickstart)
- Each contract → contract test task [P]
- Each entity → model creation task [P] 
- Each user story → integration test task
- Implementation tasks to make tests pass

**Ordering Strategy**:
- TDD order: Tests before implementation 
- Dependency order: Data fetching -> Strategy adapter -> Backtesting engine -> API -> Dashboard.
- Mark [P] for parallel execution (independent files)

**Estimated Output**: 25-30 numbered, ordered tasks in tasks.md

**IMPORTANT**: This phase is executed by the /tasks command, NOT by /plan

## Phase 3+: Future Implementation
*These phases are beyond the scope of the /plan command*

**Phase 3**: Task execution (/tasks command creates tasks.md)  
**Phase 4**: Implementation (execute tasks.md following constitutional principles)  
**Phase 5**: Validation (run tests, execute quickstart.md, performance validation)

## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| [e.g., 4th project] | [current need] | [why 3 projects insufficient] |
| [e.g., Repository pattern] | [specific problem] | [why direct DB access insufficient] |


## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [ ] Phase 0: Research complete (/plan command)
- [ ] Phase 1: Design complete (/plan command)
- [ ] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [ ] Initial Constitution Check: PASS
- [ ] Post-Design Constitution Check: PASS
- [ ] All NEEDS CLARIFICATION resolved
- [ ] Complexity deviations documented

---
*Based on Constitution v2.1.1 - See `/memory/constitution.md`*