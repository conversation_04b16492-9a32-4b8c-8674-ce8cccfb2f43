```yaml
openapi: 3.0.0
info:
  title: Backtesting API
  version: 1.0.0
paths:
  /backtests/run:
    post:
      summary: Run a new backtest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BacktestRunRequest'
      responses:
        '202':
          description: Backtest started
          content:
            application/json:
              schema:
                type: object
                properties:
                  run_id:
                    type: string
  /backtests/{run_id}/status:
    get:
      summary: Get backtest status
      parameters:
        - name: run_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Backtest status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
  /backtests/{run_id}/results:
    get:
      summary: Get backtest results
      parameters:
        - name: run_id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Backtest results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BacktestResult'
components:
  schemas:
    BacktestRunRequest:
      type: object
      properties:
        strategy:
          type: string
        symbol:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        initial_capital:
          type: number
        timeframe:
          type: string
    BacktestResult:
      type: object
      properties:
        run_id:
          type: string
        strategy:
          type: string
        symbol:
          type: string
        timeframe:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date
        initial_capital:
          type: number
        performance_metrics:
          type: object
        trade_history:
          type: array
          items:
            type: object
```
