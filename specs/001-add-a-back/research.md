# Research: Backtesting Functionality

This document summarizes the research and decisions made to clarify the technical implementation of the backtesting feature.

## 1. Integrating Custom ML Strategy with `backtrader`

**Decision**: A new `backtrader` strategy class will be created to wrap the existing `xgboost_strategy`.

**Rationale**: `backtrader` requires strategies to follow its own class structure (`bt.Strategy`). A wrapper is the cleanest way to integrate the existing ML model without rewriting it.

**Implementation Details**:
- The wrapper strategy will receive the historical data feed from `backtrader`.
- On each bar (or on a pre-defined interval), it will convert the `backtrader` data feed into a pandas DataFrame that `xgboost_strategy.generate_features` can process.
- It will call the `predict` method of the loaded XGBoost model to get a trading signal.
- Based on the signal, it will execute `self.buy()` or `self.sell()` orders within the `backtrader` engine.
- The risk management logic from `Hedgehog.engage_market` will be adapted into the `backtrader` strategy to calculate position sizing.

## 2. Standard Performance Metrics

**Decision**: The following performance metrics will be calculated and displayed:
- Net Profit/Loss (Total PnL)
- Sharpe Ratio
- Maximum Drawdown
- Win/Loss Ratio
- Total Number of Trades
- Average Trade Profit/Loss

**Rationale**: These are standard, widely-accepted metrics for evaluating the performance of a trading strategy. `backtrader` provides built-in analyzers that make calculating these straightforward.

**Implementation Details**:
- The following `backtrader` analyzers will be used:
  - `bt.analyzers.Sharpe`
  - `bt.analyzers.DrawDown`
  - `bt.analyzers.TradeAnalyzer`
  - `bt.analyzers.Returns`

## 3. Supported Timeframes

**Decision**: The backtesting feature will initially support the following timeframes: `1h`, `4h`, and `1d`.

**Rationale**: This aligns with the clarification in the feature specification and provides a good range for short-term and medium-term strategy analysis. `python-binance` readily provides data for these intervals.

## 4. Plotly Dash Application Structure

**Decision**: The Dash application will be structured into a main `app.py` file and a `views.py` file for generating plots.

**Rationale**: This separation of concerns makes the code cleaner. `app.py` will handle the application layout and callbacks, while `views.py` will contain the logic for creating the specific `Plotly` figures.

**Implementation Details**:
- `app/dashboard/app.py`: Will contain the Dash app instance, the overall layout (e.g., dropdowns to select backtest results), and the callbacks to update the graphs.
- `app/dashboard/views.py`: Will contain functions that take backtest results (trades, performance metrics) and return `plotly.graph_objects.Figure` objects to be displayed in the dashboard.

## 5. Performance Goals

**Decision**: The target for a 1-year backtest on 1-hour data is to complete execution in **under 5 minutes**.

**Rationale**: While this is an initial estimate, it sets a reasonable performance benchmark to aim for during development. This ensures the backtesting process is efficient enough for iterative strategy development. Performance will be profiled and optimized if necessary.
