# Quickstart: Backtesting

This guide provides instructions on how to run a backtest using the new functionality.

## Running a Backtest via CLI

A command-line interface will be provided to initiate backtests.

**Command**:

```bash
ython -m app.cli.backtest --strategy xgboost --symbol BTCUSDT --start-date 2023-01-01 --end-date 2023-12-31 --initial-capital 10000 --timeframe
 1h
```

**Arguments**:

- `--strategy`: The name of the strategy to backtest (e.g., `xgboost`).
- `--symbol`: The trading symbol (e.g., `BTCUSDT`).
- `--start`: The start date for the backtest (YYYY-MM-DD).
- `--end`: The end date for the backtest (YYYY-MM-DD).
- `--capital`: The initial capital for the backtest.
- `--timeframe`: The timeframe to use (e.g., `1h`, `4h`, `1d`).

The results of the backtest will be saved to a file and the path will be printed to the console.

## Accessing Results via API

The backtesting results can also be accessed via the FastAPI application.

**Endpoints**:

- `POST /backtests/run`: Start a new backtest.
  - **Body**:
    ```json
    {
      "strategy": "xgboost",
      "symbol": "BTCUSDT",
      "start_date": "2023-01-01",
      "end_date": "2023-12-31",
      "initial_capital": 10000,
      "timeframe": "1h"
    }
    ```
- `GET /backtests/{run_id}/status`: Get the status of a running backtest.
- `GET /backtests/{run_id}/results`: Get the results of a completed backtest.

## Viewing Results in the Dashboard

1. Ensure the FastAPI application is running.
2. Navigate to `http://localhost:2222/dashboard/` in your web browser.
3. Select a completed backtest run from the dropdown menu to view the performance charts and trade history.
