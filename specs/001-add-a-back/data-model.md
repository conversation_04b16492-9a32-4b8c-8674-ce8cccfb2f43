# Data Model: Backtesting

This document defines the data structures for the backtesting feature.

## `BacktestResult`

Represents the outcome of a single backtest run. This will be stored as a JSON object.

**Fields**:

- `run_id` (string, UUID): A unique identifier for the backtest run.
- `strategy` (string): The name of the strategy that was tested.
- `symbol` (string): The trading symbol (e.g., "BTCUSDT").
- `timeframe` (string): The timeframe used for the backtest (e.g., "1h", "4h", "1d").
- `start_date` (string, ISO 8601): The start date of the backtest period.
- `end_date` (string, ISO 8601): The end date of the backtest period.
- `initial_capital` (float): The starting capital for the backtest.
- `performance_metrics` (object): A dictionary containing the calculated performance metrics.
  - `net_pnl` (float): Net Profit/Loss.
  - `sharpe_ratio` (float): The Sharpe Ratio.
  - `max_drawdown` (float): The maximum drawdown.
  - `win_loss_ratio` (float): The ratio of winning trades to losing trades.
  - `total_trades` (integer): The total number of trades executed.
  - `avg_trade_pnl` (float): The average profit or loss per trade.
- `trade_history` (array of objects): A list of all trades executed during the backtest. Each trade object will have the following structure:
  - `type` (string): "BUY" or "SELL".
  - `price` (float): The execution price.
  - `size` (float): The size of the trade.
  - `timestamp` (string, ISO 8601): The timestamp of the trade execution.
  - `pnl` (float): The profit or loss for this trade (if closed).
  - `value` (float): The total value of the trade.
