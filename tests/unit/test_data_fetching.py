import pytest
from unittest.mock import patch, MagicMock
from app.services.backtesting.data import fetch_historical_data
import pandas as pd

@patch('app.services.backtesting.data.Client')
def test_fetch_historical_data(mock_binance_client):
    """
    Tests that historical data is fetched and processed correctly.
    """
    # Arrange
    mock_api = MagicMock()
    mock_binance_client.return_value = mock_api
    
    # Sample data from Binance API
    mock_klines = [
        [1672531200000, '20000', '20100', '19900', '20050', '100', 1672534799999, '2005000', 50, '50', '1002500', '0'],
        [1672534800000, '20050', '20200', '20000', '20150', '120', 1672538399999, '2418000', 60, '70', '1410500', '0']
    ]
    mock_api.get_historical_klines.return_value = mock_klines

    # Act
    df = fetch_historical_data("BTCUSDT", "1h", "2023-01-01", "2023-01-02")

    # Assert
    mock_api.get_historical_klines.assert_called_once_with("BTCUSDT", "1h", "2023-01-01", "2023-01-02")
    
    assert not df.empty
    assert len(df) == 2
    assert 'timestamp' in df.columns
    assert pd.api.types.is_datetime64_any_dtype(df['timestamp'])
    assert df['open'][0] == 20000

