import pytest
from fastapi.testclient import TestClient
from app.main import app
import time
import uuid

client = TestClient(app)

def test_run_backtest_endpoint_success():
    """
    Tests that the /backtests/run endpoint successfully accepts a request
    and returns a 202 status code with a run_id.
    """
    response = client.post("/backtests/run", json={
        "strategy": "xgboost_v1",
        "symbol": "BTCUSDT",
        "start_date": "2023-01-01",
        "end_date": "2023-01-02", # Short duration for faster test
        "initial_capital": 10000,
        "timeframe": "1h"
    })
    assert response.status_code == 202
    data = response.json()
    assert "run_id" in data
    # Validate that run_id is a valid UUID
    try:
        uuid.UUID(data["run_id"])
    except ValueError:
        pytest.fail("The 'run_id' is not a valid UUID.")

def test_get_status_for_nonexistent_run():
    """
    Tests that querying the status of a nonexistent run_id returns a 404 error.
    """
    non_existent_run_id = str(uuid.uuid4())
    response = client.get(f"/backtests/{non_existent_run_id}/status")
    assert response.status_code == 404
    assert response.json() == {"detail": "Backtest run not found"}

def test_get_results_for_nonexistent_run():
    """
    Tests that querying the results of a nonexistent run_id returns a 404 error.
    """
    non_existent_run_id = str(uuid.uuid4())
    response = client.get(f"/backtests/{non_existent_run_id}/results")
    assert response.status_code == 404
    assert response.json() == {"detail": "Backtest results not found"}

@pytest.mark.integration
def test_full_backtest_lifecycle():
    """
    Tests the full lifecycle of a backtest:
    1. Start a backtest.
    2. Poll for its status until completion.
    3. Retrieve the results.
    """
    # 1. Start the backtest
    start_response = client.post("/backtests/run", json={
        "strategy": "xgboost_v1",
        "symbol": "BTCUSDT",
        "start_date": "2023-01-01",
        "end_date": "2023-01-03", # A slightly longer but still short period
        "initial_capital": 10000,
        "timeframe": "1h"
    })
    assert start_response.status_code == 202
    run_id = start_response.json()["run_id"]

    # 2. Poll for status
    timeout = 60  # seconds
    start_time = time.time()
    status = ""
    while time.time() - start_time < timeout:
        status_response = client.get(f"/backtests/{run_id}/status")
        assert status_response.status_code == 200
        status = status_response.json()["status"]
        if status == "completed" or status.startswith("failed"):
            break
        time.sleep(2)
    
    assert status == "completed", f"Backtest did not complete in time. Final status: {status}"

    # 3. Retrieve results
    results_response = client.get(f"/backtests/{run_id}/results")
    assert results_response.status_code == 200
    results = results_response.json()

    # Validate the structure of the results
    assert results["symbol"] == "BTCUSDT"
    assert "performance_metrics" in results
    assert "final_portfolio_value" in results
    assert "trade_history" in results
    assert isinstance(results["trade_history"], list)
    assert "plot_html" in results
    # A non-empty plot will start with a div
    assert results["plot_html"].startswith("<div>")
