import pytest
from fastapi.testclient import TestClient
from app.main import app
import time
import uuid

client = TestClient(app)

@pytest.mark.integration
def test_full_backtesting_flow():
    """
    This integration test covers the full backtesting process:
    1.  Initiates a backtest via the API.
    2.  Polls for the status until the backtest is complete.
    3.  Retrieves the final results.
    4.  Performs high-level validation on the results to ensure the system works end-to-end.
    """
    # 1. Start the backtest
    start_response = client.post("/backtests/run", json={
        "strategy": "xgboost_v1",
        "symbol": "BTCUSDT",
        "start_date": "2023-01-01",
        "end_date": "2023-01-03",
        "initial_capital": 10000,
        "timeframe": "1h"
    })
    assert start_response.status_code == 202
    run_id = start_response.json()["run_id"]

    # 2. Poll for status
    timeout = 60  # seconds
    start_time = time.time()
    status = ""
    while time.time() - start_time < timeout:
        status_response = client.get(f"/backtests/{run_id}/status")
        assert status_response.status_code == 200
        status = status_response.json()["status"]
        if status == "completed" or status.startswith("failed"):
            break
        time.sleep(2)

    assert status == "completed", f"Backtest did not complete in time. Final status: {status}"

    # 3. Retrieve results
    results_response = client.get(f"/backtests/{run_id}/results")
    assert results_response.status_code == 200
    results = results_response.json()

    # 4. Validate the results
    assert results["run_id"]
    assert results["strategy"] == "xgboost_v1"
    assert results["symbol"] == "BTCUSDT"
    assert results["initial_capital"] == 10000
    assert "final_portfolio_value" in results
    assert "performance_metrics" in results
    assert "total_trades" in results["performance_metrics"]
    assert "trade_history" in results
    assert "plot_html" in results
    assert results["plot_html"].startswith("<div>")

