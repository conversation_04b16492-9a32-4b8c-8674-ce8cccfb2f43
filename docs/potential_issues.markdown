### Timezone misalignmets
- ***Problem**: the is a chance the hours and date can be misaligned during train time and inference time.This could lead to incorrect predictions or worse peformance.
- ***Solution***: Ensure that both training and inference times are in the same timezone. This can be done by setting the timezone in your environment variables or by using libraries like `pytz` in Python

### Time drift between db update and model inference time
- ***Problem**: If there is a significant time difference between when data is updated in the database and when it is used for model inference, this could lead to outdated predictions.
- ***Solution***: Ensure that the time at which data is fetched from the database is as close as possible to the time of inference. This can be done by using real-time databases or by fetching data just before making predictions by using the history object used during training instead of recalling data from the database.

- Simplifies code and reduces latency
- However data is not reatime so this wont work

### Data drift
- **Problem**: Data drift occurs when the distribution of data changes over time, which can lead to models becoming less accurate.