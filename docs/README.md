# Hedgehog Trading System Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [Setup & Installation](#setup--installation)
5. [Configuration](#configuration)
6. [API Reference](#api-reference)
7. [Trading Logic](#trading-logic)
8. [Risk Management](#risk-management)
9. [Monitoring & Maintenance](#monitoring--maintenance)
10. [Security Considerations](#security-considerations)

## System Overview
Hedgehog is an automated cryptocurrency trading system that implements real-time market data processing, ML-based predictions, and risk management strategies. The system uses the Alpaca API for crypto trading and implements a sophisticated XGBoost-based prediction model.

### Key Features
- Real-time crypto market data streaming
- Automated trading decisions
- Risk management and position sizing
- ML-based prediction model
- Robust error handling and recovery

## Architecture
