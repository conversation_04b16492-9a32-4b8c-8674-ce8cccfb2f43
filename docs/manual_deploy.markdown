### Push to registery
```cmd
<!-- az login --use-device-code
az acr login --name sainpse -->

echo build...
docker build -t hedgehog-backend .
docker tag hedgehog-backend thesainpseinstitute/hedgehog-backend:latest
docker push thesainpseinstitute/hedgehog-backend:latest
docker run --env-file .env -p 2222:80 --name hedgehog-backend-container thesainpseinstitute/hedgehog-backend:latest
```
```cmd
 az container create --resource-group sainpse --name hedgehogbackend --image sainpse.azurecr.io/hedgehog-backend:latest --registry-login-server sainpse.azurecr.io --registry-username "sainpse" --registry-password "****************************************************" --dns-name-label hedgehog-backend-api --ports 80 --ip-address public --cpu 1 --memory 1.5 --environment-variables ALPACA_DEV_SECRET_KEY="wLXQopw6t2txqgDvwFEVSvgrKpLAXTFhm8C5dW8X" ALPACA_DEV_ACCESS_KEY="AKJDL1XGBEREMIQA9ZHD" CONNECTION_STRING_MONGODB="***********************************************************************************************************************************************************************************************************************************************"

```